<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('BD促销批量工作区')" />
    <th:block th:include="include :: bootstrap-select-css" />
    <style>
        .deal-card {
            border: 1px solid #e7eaec;
            border-radius: 4px;
            margin-bottom: 15px;
            background: #fff;
        }
        .deal-card-header {
            background: #f8f8f9;
            padding: 10px 15px;
            border-bottom: 1px solid #e7eaec;
            border-radius: 4px 4px 0 0;
        }
        .deal-card-body {
            padding: 15px;
        }
        .deal-summary {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .deal-actions {
            white-space: nowrap;
        }
        .asin-count {
            color: #1ab394;
            font-weight: bold;
        }
        .deal-status {
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
        }
        .status-draft { background: #f8ac59; color: white; }
        .status-ready { background: #1ab394; color: white; }
        .status-error { background: #ed5565; color: white; }
        .workspace-header {
            background: #f8f8f9;
            padding: 20px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .workspace-stats {
            display: flex;
            justify-content: space-around;
            text-align: center;
        }
        .stat-item {
            flex: 1;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #1ab394;
        }
        .stat-label {
            color: #676a6c;
            font-size: 12px;
        }

        /* 促销要求说明区域样式 */
        .requirements-section {
            margin-bottom: 20px;
        }
        .requirements-list {
            margin: 15px 0 0 0;
            padding-left: 20px;
        }
        .requirements-list li {
            margin-bottom: 8px;
            line-height: 1.5;
        }

        /* 错误记录高亮样式 */
        .highlight-error {
            animation: highlightError 3s ease-in-out;
            border: 2px solid #ed5565 !important;
            box-shadow: 0 0 10px rgba(237, 85, 101, 0.3) !important;
        }

        @keyframes highlightError {
            0% {
                background-color: rgba(237, 85, 101, 0.1);
                transform: scale(1);
            }
            50% {
                background-color: rgba(237, 85, 101, 0.05);
                transform: scale(1.02);
            }
            100% {
                background-color: transparent;
                transform: scale(1);
            }
        }

        /* 价格单元格样式 */
        .price-cell {
            position: relative;
        }
        .price-cell input {
            margin-bottom: 5px;
            font-size: 12px;
            height: 28px;
        }
        .price-cell small {
            display: block;
            font-size: 10px;
            color: #999;
            margin-top: 2px;
        }
        .max-price {
            color: #1ab394 !important;
            font-weight: bold;
        }
        .min-funding {
            color: #f8ac59 !important;
            font-weight: bold;
        }
        .promotion-price-input.is-invalid {
            border-color: #ed5565;
            box-shadow: 0 0 0 0.2rem rgba(237, 85, 101, 0.25);
        }

        /* 表格样式优化 */
        .table-responsive {
            margin-top: 10px;
        }
        .table th {
            background-color: #f8f8f9;
            font-size: 12px;
            font-weight: bold;
            text-align: center;
            vertical-align: middle;
        }
        .table td {
            font-size: 12px;
            vertical-align: middle;
            text-align: center;
        }
        .table td:nth-child(4) {
            text-align: left; /* 标题列左对齐 */
        }

        /* 折叠图标样式 */
        .deal-toggle-icon {
            transition: transform 0.3s ease;
            margin-right: 8px;
        }
        .deal-card-header:hover {
            background-color: #f0f0f0;
        }

        /* 底部按钮区域样式 */
        .bottom-actions {
            background: #f8f8f9;
            border-top: 1px solid #e7eaec;
            margin-top: 30px;
        }

        /* 按钮样式优化 */
        .btn-lg {
            padding: 12px 30px;
            font-size: 16px;
            border-radius: 6px;
        }

        /* 加载状态样式 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .loading-content {
            text-align: center;
            padding: 30px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .loading-spinner {
            font-size: 32px;
            color: #1ab394;
            margin-bottom: 15px;
        }

        .loading-text {
            color: #676a6c;
            font-size: 16px;
            margin: 0;
        }
    </style>
</head>
<body class="gray-bg">
    <div class="wrapper wrapper-content animated fadeInRight">
        <!-- 促销要求说明区域 -->
        <div class="requirements-section">
            <div class="alert alert-info">
                <h4><i class="fa fa-info-circle"></i> 促销要求</h4>
                <ul class="requirements-list">
                    <li>1</li>
                    <li>2</li>
                </ul>
            </div>
        </div>

        <!-- 操作工具栏 -->
        <div class="row">
            <div class="col-sm-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>操作工具栏</h5>
                    </div>
                    <div class="ibox-content">
                        <button type="button" class="btn btn-primary" onclick="addNewDeal()">
                            <i class="fa fa-plus"></i> 新建BD促销
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- BD促销记录列表 -->
        <div class="row">
            <div class="col-sm-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>BD促销记录列表</h5>
                        <div class="ibox-tools">
                            <span class="label label-info" id="list-summary">暂无记录</span>
                        </div>
                    </div>
                    <div class="ibox-content" id="deals-container">
                        <div class="text-center text-muted" id="empty-state" style="padding: 50px;">
                            <i class="fa fa-cube" style="font-size: 48px; color: #ddd;"></i>
                            <h4>工作区为空</h4>
                            <p>点击"新建BD促销"开始创建您的第一个促销记录</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部确认取消按钮 -->
        <div class="bottom-actions">
            <div class="text-center" style="padding: 20px 0;">
                <button type="button" class="btn btn-success btn-lg" onclick="confirmAction()" style="margin-right: 20px;">
                    <i class="fa fa-check"></i> 确认
                </button>
                <button type="button" class="btn btn-default btn-lg" onclick="cancelAction()">
                    <i class="fa fa-times"></i> 取消
                </button>
            </div>
        </div>
    </div>

    <th:block th:include="include :: footer" />
    <th:block th:include="include :: bootstrap-select-js" />
    <script th:inline="javascript">
        var prefix = ctx + "promotion/record";
        var toolPrefix = ctx + "promotion/tool";

        // BD促销记录列表
        var dealList = [];

        // 唯一ID生成器
        var dealIdCounter = 1;
        
        $(function() {
            // 初始化页面
            updateWorkspaceStats();
            renderDealList();
        });
        
        // 新建BD促销
        function addNewDeal() {
            // 预分配ID
            var newId = "deal_" + dealIdCounter++;

            // 创建空的促销数据
            var emptyDeal = {
                promotionName: '',
                publishType: '',
                eventType: 1, // 默认为自定义日期
                startDateUtc: '',
                endDateUtc: '',
                remark: '',
                asinList: []
            };

            // 生成唯一存储key：时间戳 + 随机数确保唯一性
            var timestamp = Date.now();
            var random = Math.random().toString(36).substr(2, 4);
            var storageKey = "bd_promotion_edit_" + newId + "_" + timestamp + "_" + random;

            // 存储到sessionStorage
            sessionStorage.setItem(storageKey, JSON.stringify(emptyDeal));

            var options = {
                title: '新建BD促销',
                url: prefix + "/addModal?storageKey=" + encodeURIComponent(storageKey),
                width: '1200',
                height: '700',
                callBack: function(index, layero) {
                    try {
                        // 获取子页面的window对象
                        var iframe = layero.find('iframe')[0];
                        var childWindow = iframe.contentWindow;

                        // 主动调用子页面的submitHandler函数进行验证和数据准备
                        if (childWindow && typeof childWindow.submitHandler === 'function') {
                            var submitResult = childWindow.submitHandler(index, layero);

                            if (submitResult && childWindow.dealData) {
                                // 验证通过且有数据，处理并关闭窗口
                                var newDeal = childWindow.dealData;
                                newDeal.id = newId; // 设置预分配的ID
                                addOrUpdateDeal(newDeal);

                                // 清理并关闭窗口
                                sessionStorage.removeItem(storageKey);
                                $.modal.close(index);
                            } else {
                                return false; // 阻止关闭
                            }
                        } else {
                            return false; // 阻止关闭
                        }
                    } catch (e) {
                        console.error('调用子页面函数失败:', e);
                        return false; // 阻止关闭
                    }
                }
            };
            $.modal.openOptions(options);
        }
        
        // 编辑BD促销
        function editDeal(dealId) {
            var deal = findDealById(dealId);
            if (!deal) {
                $.modal.alertError("找不到指定的BD促销记录");
                return;
            }

            // 精简数据，只保留编辑必需的字段
            var cleanDeal = {
                promotionName: deal.promotionName || '',
                publishType: deal.publishType || '',
                eventType: deal.eventType || 1, // 默认为自定义日期
                startDateUtc: deal.startDateUtc || '',
                endDateUtc: deal.endDateUtc || '',
                remark: deal.remark || '',
                asinList: (deal.asinList || []).map(function(asin) {
                    return {
                        id: asin.id,
                        platformGoodsId: asin.platformGoodsId,
                        platformGoodsCode: asin.platformGoodsCode,
                        title: asin.title,
                        standardPrice: asin.standardPrice,
                        referencePrice: asin.referencePrice,
                        promotionPrice: asin.promotionPrice,
                        funding: asin.funding
                    };
                })
            };

            // 生成唯一存储key：时间戳 + 随机数确保唯一性
            var timestamp = Date.now();
            var random = Math.random().toString(36).substr(2, 4);
            var storageKey = "bd_promotion_edit_" + dealId + "_" + timestamp + "_" + random;

            // 存储到sessionStorage
            sessionStorage.setItem(storageKey, JSON.stringify(cleanDeal));

            var options = {
                title: '编辑BD促销',
                url: prefix + "/addModal?storageKey=" + encodeURIComponent(storageKey),
                width: '1200',
                height: '700',
                callBack: function(index, layero) {
                    try {
                        // 获取子页面的window对象
                        var iframe = layero.find('iframe')[0];
                        var childWindow = iframe.contentWindow;

                        // 主动调用子页面的submitHandler函数进行验证和数据准备
                        if (childWindow && typeof childWindow.submitHandler === 'function') {
                            var submitResult = childWindow.submitHandler(index, layero);

                            if (submitResult && childWindow.dealData) {
                                // 验证通过且有数据，处理并关闭窗口
                                var updatedDeal = childWindow.dealData;
                                updatedDeal.id = dealId; // 保持原有ID
                                addOrUpdateDeal(updatedDeal);

                                // 清理并关闭窗口
                                sessionStorage.removeItem(storageKey);
                                $.modal.close(index);
                            } else {
                                // 验证失败或没有数据，不关闭窗口
                                console.warn('表单验证失败或未获取到数据，窗口保持打开状态');
                                return false; // 阻止关闭
                            }
                        } else {
                            console.error('无法找到子页面的submitHandler函数');
                            return false; // 阻止关闭
                        }
                    } catch (e) {
                        console.error('调用子页面函数失败:', e);
                        return false; // 阻止关闭
                    }
                }
            };
            $.modal.openOptions(options);
        }
        
        // 添加或更新BD促销记录
        function addOrUpdateDeal(deal) {
            // 检查是否是现有记录的更新（通过在dealList中查找ID）
            var existingIndex = dealList.findIndex(function(item) {
                return item.id === deal.id;
            });

            if (existingIndex >= 0) {
                // 调用更新逻辑
                proceedWithUpdateDeal(deal);
            } else {
                proceedWithAddDeal(deal);
            }
        }



        // 获取竞争摘要信息
        function fetchCompetitiveSummary(asinList) {
            return new Promise(function(resolve, reject) {
                if (!asinList || asinList.length === 0) {
                    reject(new Error('ASIN列表不能为空'));
                    return;
                }

                $.ajax({
                    url: toolPrefix + "/batchGetCompetitiveSummary",
                    type: "POST",
                    contentType: "application/json",
                    data: JSON.stringify({
                        asinList: asinList
                    }),
                    success: function(response) {
                        if (response.code === 0) {
                            resolve(response.data);
                        } else {
                            reject(new Error(response.msg || '获取竞争摘要失败'));
                        }
                    },
                    error: function(xhr, status, error) {
                        if (status === 'timeout') {
                            reject(new Error('请求超时，请稍后重试'));
                        } else {
                            reject(new Error('网络请求失败: ' + error));
                        }
                    }
                });
            });
        }

        // 获取默认折扣比例
        function getDefaultDiscountRateByEventType(eventType) {
            switch (eventType) {
                case 1: return 10; // 自定义日期：10%
                case 2: return 15; // 会员日：15%
                case 3: return 15; // 黑五：15%
                default: return 10; // 默认：10%
            }
        }

        // 继续添加新记录
        function proceedWithAddDeal(deal) {
            // ID已经在addNewDeal或其他地方预分配了，不要重复分配
            if (!deal.id) {
                deal.id = 'deal_' + dealIdCounter++;
            }
            deal.status = 'draft';
            deal.createTime = new Date().toLocaleString();

            // 如果有ASIN列表，先获取参考价
            if (deal.asinList && deal.asinList.length > 0) {
                fetchReferencePricesAndAddDeal(deal);
            } else {
                // 没有ASIN直接添加
                finalizeDealAdd(deal);
            }
        }

        // 获取真实参考价并添加记录
        function fetchReferencePricesAndAddDeal(deal) {
            // 提取所有ASIN的platformGoodsId
            var platformGoodsIds = deal.asinList.map(function(asin) {
                return asin.platformGoodsId;
            });

            // 显示loading提示
            $.modal.loading("正在获取价格信息...");

            // 批量获取价格信息
            fetchCompetitiveSummary(platformGoodsIds)
                .then(function(summaryData) {
                    console.log('获取价格信息成功:', summaryData);

                    // 根据事件类型确定默认折扣比例
                    var defaultDiscountRate = getDefaultDiscountRateByEventType(parseInt(deal.eventType) || 1);

                    // 处理每个ASIN的价格信息
                    for (var i = 0; i < deal.asinList.length; i++) {
                        var asin = deal.asinList[i];
                        var competitiveInfo = summaryData[asin.platformGoodsId];

                        if (competitiveInfo && competitiveInfo.referencePrice) {
                            // 使用真实的参考价格
                            asin.referencePrice = parseFloat(competitiveInfo.referencePrice);
                        } else {
                            asin.referencePrice = asin.vcListPrice
                            if (!asin.referencePrice) {
                                asin.referencePrice = asin.standardPrice
                            } 
                        }
                        if (!asin.referencePrice) {
                            asin.hasReferencePrice = false;
                            continue
                        }
                        // 根据事件类型计算maxPrice
                        var discountRate = getDefaultDiscountRateByEventType(parseInt(deal.eventType) || 1);
                        asin.maxPrice = parseFloat((asin.referencePrice * (1 - discountRate / 100)).toFixed(2));

                        // 预计算固定值
                        asin.minFunding = calculateMinFundingByEventType(asin, deal.eventType);

                        // 计算80%funding上限和促销价下限
                        var maxAllowedFunding = calculateMaxAllowedFunding(asin.referencePrice);
                        var minAllowedPrice = calculateMinAllowedPrice(asin.referencePrice);

                        // 智能设置默认促销价
                        var targetDiscountPrice = asin.referencePrice * (1 - defaultDiscountRate / 100);
                        targetDiscountPrice = Math.max(targetDiscountPrice, minAllowedPrice);

                        if (floatLessThan(asin.maxPrice, targetDiscountPrice)) {
                            // 接口限价比较严格，以maxPrice为准
                            asin.promotionPrice = parseFloat(Math.max(asin.maxPrice, minAllowedPrice).toFixed(2));
                            asin.funding = parseFloat((asin.referencePrice - asin.promotionPrice).toFixed(2));
                            asin.funding = parseFloat(Math.min(asin.funding, maxAllowedFunding).toFixed(2));
                        } else {
                            // 接口限价比较宽松，以业务折扣为准
                            asin.promotionPrice = parseFloat(targetDiscountPrice.toFixed(2));
                            asin.funding = parseFloat((asin.referencePrice - targetDiscountPrice).toFixed(2));
                            asin.funding = parseFloat(Math.min(asin.funding, maxAllowedFunding).toFixed(2));
                        }
                    }

                    $.modal.closeLoading();
                    finalizeDealAdd(deal);
                })
                .catch(function(error) {
                    $.modal.closeLoading();
                    $.modal.alertError('获取价格信息失败: ' + error.message + '，将使用默认状态添加ASIN');
                });
        }



        // 完成记录添加
        // 根据事件类型查找是否存在可合并的记录
        function findExistingDealByEventType(deal) {
            var eventType = parseInt(deal.eventType) || 1;

            if (eventType === 1) {
                // 自定义日期：按时间周期 + 发布类型 + 事件类型合并
                var existingIndex = dealList.findIndex(function(item) {
                    return item.eventType === 1 &&
                           item.startDateUtc === deal.startDateUtc &&
                           item.endDateUtc === deal.endDateUtc &&
                           item.publishType === deal.publishType;
                });
                return existingIndex;
            } else {
                // 会员日(eventType=2)或黑五(eventType=3)：不合并，直接创建新记录
                console.log('会员日/黑五类型，不进行合并，直接创建新记录');
                return -1;
            }
        }

        function finalizeDealAdd(deal) {
            // 根据事件类型查找是否存在可合并的记录
            var existingIndex = findExistingDealByEventType(deal);

            if (existingIndex >= 0) {
                console.warn('发现可合并的记录，将合并ASIN而不是覆盖');
                console.warn('现有记录:', dealList[existingIndex].promotionName);
                console.warn('新记录:', deal.promotionName);

                // 合并ASIN列表，保留原有ASIN的价格信息
                var existingDeal = dealList[existingIndex];
                mergeDealsForAdd(existingDeal, deal);
                existingDeal.status = validateDeal(existingDeal);
            } else {
                // 验证数据完整性
                deal.status = validateDeal(deal);

                // 添加新记录
                dealList.push(deal);
                console.log('新记录已添加，dealList长度:', dealList.length);
                console.log('所有记录的促销名称:', dealList.map(function(d) { return d.promotionName; }));
            }

            // 刷新显示
            renderDealList();
            updateWorkspaceStats();
        }

        // 继续更新现有记录
        function proceedWithUpdateDeal(deal) {
            // 查找是否已存在
            var existingIndex = dealList.findIndex(function(item) {
                return item.id === deal.id;
            });

            if (existingIndex >= 0) {
                var originalDeal = dealList[existingIndex];

                // 智能判断是否需要重新获取价格信息
                var newAsins = findNewAsins(originalDeal.asinList || [], deal.asinList || []);

                if (newAsins.length > 0) {
                    // 有新增ASIN，需要为新增ASIN获取价格信息
                    console.log('发现新增ASIN，数量:', newAsins.length);
                    fetchPricesForNewAsinsAndUpdate(deal, existingIndex, newAsins);
                } else {
                    // 没有新增ASIN，直接更新（保持现有价格信息）
                    console.log('没有新增ASIN，直接更新记录');

                    // 保留原有ASIN的完整价格信息，只更新基础字段
                    mergeBasicFieldsOnly(originalDeal, deal);

                    originalDeal.status = validateDeal(originalDeal);
                    renderDealList();
                    updateWorkspaceStats();
                    $.modal.msgSuccess('BD促销记录更新成功');
                }
            }
        }



        // 合并新建记录（用于finalizeDealAdd中的记录合并）
        function mergeDealsForAdd(existingDeal, newDeal) {
            // 更新基础字段（使用新记录的信息）
            existingDeal.promotionName = newDeal.promotionName;
            existingDeal.publishType = newDeal.publishType;
            existingDeal.eventType = newDeal.eventType;
            existingDeal.startDateUtc = newDeal.startDateUtc;
            existingDeal.endDateUtc = newDeal.endDateUtc;
            existingDeal.remark = newDeal.remark;
            existingDeal.site = newDeal.site;

            // 合并ASIN列表：将新ASIN添加到现有列表中
            if (newDeal.asinList && newDeal.asinList.length > 0) {
                if (!existingDeal.asinList) {
                    existingDeal.asinList = [];
                }

                // 创建现有ASIN的查找集合
                var existingAsinSet = new Set();
                existingDeal.asinList.forEach(function(asin) {
                    var key = asin.platformGoodsId + '_' + asin.platformGoodsCode;
                    existingAsinSet.add(key);
                });

                // 添加新的ASIN（避免重复）
                newDeal.asinList.forEach(function(newAsin) {
                    var key = newAsin.platformGoodsId + '_' + newAsin.platformGoodsCode;
                    if (!existingAsinSet.has(key)) {
                        existingDeal.asinList.push(newAsin);
                    }
                });

                console.log('合并ASIN列表完成，总数量:', existingDeal.asinList.length);
            }
        }

        // 只更新基础字段，保留原有ASIN的完整价格信息
        function mergeBasicFieldsOnly(originalDeal, newDeal) {
            // 更新基础字段
            originalDeal.promotionName = newDeal.promotionName;
            originalDeal.publishType = newDeal.publishType;
            originalDeal.eventType = newDeal.eventType;
            originalDeal.startDateUtc = newDeal.startDateUtc;
            originalDeal.endDateUtc = newDeal.endDateUtc;
            originalDeal.remark = newDeal.remark;
            originalDeal.site = newDeal.site;

            // 处理ASIN列表：保留原有ASIN的完整价格信息，只更新ASIN列表结构
            if (newDeal.asinList) {
                // 创建原有ASIN的映射表（保留完整价格信息）
                var originalAsinMap = {};
                if (originalDeal.asinList) {
                    originalDeal.asinList.forEach(function(asin) {
                        var key = asin.platformGoodsId + '_' + asin.platformGoodsCode;
                        originalAsinMap[key] = asin; // 保留完整的ASIN对象
                    });
                }

                // 构建新的ASIN列表：对于已存在的ASIN，使用原有的完整信息
                var mergedAsinList = [];
                newDeal.asinList.forEach(function(newAsin) {
                    var key = newAsin.platformGoodsId + '_' + newAsin.platformGoodsCode;
                    var originalAsin = originalAsinMap[key];

                    if (originalAsin) {
                        // 已存在ASIN：使用原有的完整价格信息
                        mergedAsinList.push(originalAsin);
                    } else {
                        // 新增ASIN：使用新的基础信息（这种情况理论上不应该发生在这个分支）
                        mergedAsinList.push(newAsin);
                    }
                });

                originalDeal.asinList = mergedAsinList;
            }

            console.log('合并基础字段完成，保留了原有ASIN的价格信息');
        }

        // 找出新增的ASIN（在新列表中存在但在原列表中不存在的ASIN）
        function findNewAsins(originalAsinList, newAsinList) {
            var newAsins = [];

            // 创建原有ASIN的查找集合（使用组合键确保唯一性）
            var originalAsinSet = new Set();
            originalAsinList.forEach(function(asin) {
                var key = asin.platformGoodsId + '_' + asin.platformGoodsCode;
                originalAsinSet.add(key);
            });

            // 找出新增的ASIN
            newAsinList.forEach(function(asin) {
                var key = asin.platformGoodsId + '_' + asin.platformGoodsCode;
                if (!originalAsinSet.has(key)) {
                    newAsins.push(asin);
                }
            });

            return newAsins;
        }

        // 只为新增ASIN获取价格信息并更新记录
        function fetchPricesForNewAsinsAndUpdate(deal, existingIndex, newAsins) {
            // 提取新增ASIN的platformGoodsId
            var newPlatformGoodsIds = newAsins.map(function(asin) {
                return asin.platformGoodsId;
            });

            // 显示loading提示
            $.modal.loading("正在获取新增ASIN的价格信息...");

            // 批量获取新增ASIN的价格信息
            fetchCompetitiveSummary(newPlatformGoodsIds)
                .then(function(summaryData) {
                    console.log('获取新增ASIN价格信息成功:', summaryData);

                    // 根据事件类型确定默认折扣比例
                    var defaultDiscountRate = getDefaultDiscountRateByEventType(parseInt(deal.eventType) || 1);
                    var discountRate = getDefaultDiscountRateByEventType(parseInt(deal.eventType) || 1);

                    // 只处理新增ASIN的价格信息
                    for (var i = 0; i < deal.asinList.length; i++) {
                        var asin = deal.asinList[i];
                        var asinKey = asin.platformGoodsId + '_' + asin.platformGoodsCode;

                        // 检查是否是新增ASIN
                        var isNewAsin = newAsins.some(function(newAsin) {
                            return newAsin.platformGoodsId === asin.platformGoodsId &&
                                   newAsin.platformGoodsCode === asin.platformGoodsCode;
                        });

                        if (isNewAsin) {
                            // 新增ASIN：计算价格信息
                            console.log('计算新增ASIN价格 - ASIN:', asinKey);
                            var competitiveInfo = summaryData[asin.platformGoodsId];

                            // 更新参考价格信息
                            if (competitiveInfo && competitiveInfo.referencePrice) {
                                asin.referencePrice = parseFloat(competitiveInfo.referencePrice);
                            } else {
                                asin.referencePrice = asin.vcListPrice || asin.standardPrice;
                            }

                            if (!asin.referencePrice) {
                                asin.hasReferencePrice = false;
                                continue;
                            }

                            // 计算价格信息
                            asin.maxPrice = parseFloat((asin.referencePrice * (1 - discountRate / 100)).toFixed(2));
                            asin.minFunding = calculateMinFundingByEventType(asin, deal.eventType);

                            var maxAllowedFunding = calculateMaxAllowedFunding(asin.referencePrice);
                            var minAllowedPrice = calculateMinAllowedPrice(asin.referencePrice);
                            var targetDiscountPrice = asin.referencePrice * (1 - defaultDiscountRate / 100);
                            targetDiscountPrice = Math.max(targetDiscountPrice, minAllowedPrice);

                            if (floatLessThan(asin.maxPrice, targetDiscountPrice)) {
                                asin.promotionPrice = parseFloat(Math.max(asin.maxPrice, minAllowedPrice).toFixed(2));
                                asin.funding = parseFloat((asin.referencePrice - asin.promotionPrice).toFixed(2));
                                asin.funding = parseFloat(Math.min(asin.funding, maxAllowedFunding).toFixed(2));
                            } else {
                                asin.promotionPrice = parseFloat(targetDiscountPrice.toFixed(2));
                                asin.funding = parseFloat((asin.referencePrice - targetDiscountPrice).toFixed(2));
                                asin.funding = parseFloat(Math.min(asin.funding, maxAllowedFunding).toFixed(2));
                            }
                        }
                        // 已存在ASIN：保持原有价格信息不变（不做任何处理）
                    }

                    // 将更新后的deal合并到原有记录中
                    var originalDeal = dealList[existingIndex];
                    mergeBasicFieldsOnly(originalDeal, deal);

                    // 验证并更新记录
                    originalDeal.status = validateDeal(originalDeal);

                    $.modal.closeLoading();
                    renderDealList();
                    updateWorkspaceStats();

                    $.modal.msgSuccess('BD促销记录更新成功');
                })
                .catch(function(error) {
                    console.error('获取新增ASIN价格信息失败:', error);
                    $.modal.closeLoading();

                    // API失败时，仍然更新记录但新增ASIN标记为无参考价
                    for (var i = 0; i < deal.asinList.length; i++) {
                        var asin = deal.asinList[i];
                        var isNewAsin = newAsins.some(function(newAsin) {
                            return newAsin.platformGoodsId === asin.platformGoodsId &&
                                   newAsin.platformGoodsCode === asin.platformGoodsCode;
                        });

                        if (isNewAsin) {
                            // 只对新增ASIN设置默认状态
                            asin.referencePrice = null;
                            asin.maxPrice = null;
                            asin.hasReferencePrice = false;
                            asin.promotionPrice = null;
                            asin.funding = null;
                            asin.minFunding = null;
                        }
                    }

                    // 将更新后的deal合并到原有记录中
                    var originalDeal = dealList[existingIndex];
                    mergeBasicFieldsOnly(originalDeal, deal);
                    originalDeal.status = validateDeal(originalDeal);

                    renderDealList();
                    updateWorkspaceStats();

                    $.modal.alertWarning('获取新增ASIN价格信息失败，已使用默认状态更新记录');
                });
        }
        
        // 验证BD促销记录（基础校验 - 用于添加时）
        function validateDeal(deal) {
            return validateDealBasic(deal);
        }

        // 基础校验（添加ASIN时使用）
        function validateDealBasic(deal) {
            var errors = [];

            // 基本字段验证
            if (!deal.promotionName || deal.promotionName.trim() === '') {
                errors.push('促销名称不能为空');
            }
            if (!deal.publishType) {
                errors.push('刊登类型不能为空');
            }
            if (!deal.eventType) {
                errors.push('事件类型不能为空');
            }

            // 根据事件类型验证日期字段
            var eventType = parseInt(deal.eventType) || 1;
            if (eventType === 1) {
                // 自定义日期：需要验证开始时间和结束时间
                if (!deal.startDateUtc || deal.startDateUtc.trim() === '') {
                    errors.push('自定义日期类型需要选择开始时间');
                }
                if (!deal.endDateUtc || deal.endDateUtc.trim() === '') {
                    errors.push('自定义日期类型需要选择结束时间');
                }

                // 验证开始时间不能包含预设文本
                if (deal.startDateUtc && (deal.startDateUtc.includes('会员日') || deal.startDateUtc.includes('黑五'))) {
                    errors.push('自定义日期类型需要选择具体的开始时间');
                }
                if (deal.endDateUtc && (deal.endDateUtc.includes('会员日') || deal.endDateUtc.includes('黑五'))) {
                    errors.push('自定义日期类型需要选择具体的结束时间');
                }
            }

            // ASIN列表验证（只验证是否有ASIN）
            if (!deal.asinList || deal.asinList.length === 0) {
                errors.push('至少需要一个ASIN');
            }

            // 设置状态
            if (errors.length === 0) {
                deal.validationErrors = null;
                return 'ready';
            } else {
                deal.validationErrors = errors;
                return 'error';
            }
        }

        // 完整校验（提交时使用）
        function validateDealComplete(deal) {
            var errors = [];

            // 先进行基础校验
            var basicStatus = validateDealBasic(deal);
            if (basicStatus === 'error' && deal.validationErrors) {
                errors = errors.concat(deal.validationErrors);
            }

            // ASIN详细字段验证
            if (deal.asinList && deal.asinList.length > 0) {
                for (var i = 0; i < deal.asinList.length; i++) {
                    var asin = deal.asinList[i];
                    var asinPrefix = 'ASIN ' + asin.platformGoodsId + ': ';

                    // 验证参考价格
                    if (!asin.referencePrice || asin.referencePrice <= 0) {
                        errors.push(asinPrefix + 'Reference price不能为空或小于等于0');
                    }

                    // 验证促销价格
                    if (!asin.promotionPrice || asin.promotionPrice <= 0) {
                        errors.push(asinPrefix + 'Deal price不能为空或小于等于0');
                    }

                    // 验证承诺数量
                    if (!asin.committedUnits || asin.committedUnits <= 0) {
                        errors.push(asinPrefix + 'Committed units不能为空或小于等于0');
                    }

                    // 验证funding
                    if (!asin.funding || asin.funding <= 0) {
                        errors.push(asinPrefix + 'Per unit funding不能为空或小于等于0');
                    }

                    // 如果有参考价格和促销价格，验证业务逻辑
                    if (asin.referencePrice && asin.promotionPrice) {
                        // 验证促销价格不能大于等于参考价格（使用精度比较）
                        if (floatGreaterOrEqual(asin.promotionPrice, asin.referencePrice)) {
                            errors.push(asinPrefix + 'Deal price必须小于参考价格');
                        }

                        // 验证促销价格不能低于参考价格的20%（使用精度比较）
                        var minAllowedPrice = asin.referencePrice * 0.2;
                        if (floatLessThan(asin.promotionPrice, minAllowedPrice)) {
                            errors.push(asinPrefix + 'Deal price不能低于参考价格的20% ($' + minAllowedPrice.toFixed(2) + ')');
                        }
                    }

                    // 如果有参考价格和funding，验证funding逻辑
                    if (asin.referencePrice && asin.funding) {
                        // 验证funding不能超过参考价格的80%（使用精度比较）
                        var maxAllowedFunding = asin.referencePrice * 0.8;
                        if (floatGreaterThan(asin.funding, maxAllowedFunding)) {
                            errors.push(asinPrefix + 'Per unit funding不能超过参考价格的80% ($' + maxAllowedFunding.toFixed(2) + ')');
                        }

                        // 验证funding不能小于最小funding（使用精度比较）
                        if (asin.minFunding && floatLessThan(asin.funding, asin.minFunding)) {
                            errors.push(asinPrefix + 'Per unit funding不能小于最小funding ($' + asin.minFunding.toFixed(2) + ')');
                        }
                    }

                    // 验证促销价格和funding的一致性
                    if (asin.referencePrice && asin.promotionPrice && asin.funding) {
                        var calculatedFunding = asin.referencePrice - asin.promotionPrice;
                        var fundingDifference = Math.abs(calculatedFunding - asin.funding);

                        // 允许0.01的误差
                        if (fundingDifference > 0.01) {
                            errors.push(asinPrefix + 'Deal price and per unit funding mismatch (calculated funding: $' + calculatedFunding.toFixed(2) + ')');
                        }
                    }
                }
            }

            // 设置状态
            if (errors.length === 0) {
                deal.validationErrors = null;
                return 'ready';
            } else {
                deal.validationErrors = errors;
                return 'error';
            }
        }
        
        // 渲染BD促销记录列表
        function renderDealList() {
            var container = $('#deals-container');
            var emptyState = $('#empty-state');

            if (dealList.length === 0) {
                console.log('dealList为空，显示空状态');
                emptyState.show();
                return;
            }

            emptyState.hide();

            var html = '';
            for (var i = 0; i < dealList.length; i++) {
                var deal = dealList[i];
                html += renderDealCard(deal);
            }

            console.log('生成的HTML长度:', html.length);
            container.html(html);
            updateListSummary();
        }
        
        // 渲染单个BD促销卡片
        function renderDealCard(deal) {
            var statusClass = 'status-' + deal.status;
            var asinCount = deal.asinList ? deal.asinList.length : 0;

            // 如果没有折叠状态，默认为展开
            if (deal.isCollapsed === undefined) {
                deal.isCollapsed = false;
            }

            var html = '<div class="deal-card" data-deal-id="' + deal.id + '">';

            // 促销记录头部（可点击折叠/展开）
            html += '<div class="deal-card-header" onclick="toggleDealDetails(\'' + deal.id + '\')" style="cursor: pointer;">';
            html += '<div class="deal-summary">';
            html += '<div>';
            html += '<h4 style="margin: 0;">';
            // 根据折叠状态显示不同的图标
            var iconClass = deal.isCollapsed ? 'fa-chevron-down' : 'fa-chevron-up';
            html += '<i class="fa ' + iconClass + ' deal-toggle-icon" id="toggle-' + deal.id + '"></i> ';
            html += (deal.promotionName || '未命名促销');
            html += '</h4>';
            html += '<small class="text-muted">';
            html += (deal.site || '') + ' | ' + getPublishTypeText(deal.publishType) + ' | ';
            html += getEventTypeText(deal.eventType) + ' | ';
            html += (deal.startDateUtc || '') + ' ~ ' + (deal.endDateUtc || '');
            html += '</small>';
            html += '</div>';
            html += '<div class="deal-actions" onclick="event.stopPropagation();">';
            html += '<span class="asin-count">' + asinCount + ' ASIN</span> ';
            html += '<button class="btn btn-xs btn-info" onclick="editDeal(\'' + deal.id + '\')" title="编辑">';
            html += '<i class="fa fa-edit"></i></button> ';
            html += '<button class="btn btn-xs btn-danger" onclick="removeDeal(\'' + deal.id + '\')" title="删除">';
            html += '<i class="fa fa-trash"></i></button>';
            html += '</div>';
            html += '</div>';
            html += '</div>';

            // 促销记录详情（根据折叠状态显示/隐藏）
            var displayStyle = deal.isCollapsed ? 'display: none;' : 'display: block;';
            html += '<div class="deal-card-body" id="details-' + deal.id + '" style="' + displayStyle + '">';

            // 显示验证错误
            if (deal.status === 'error' && deal.validationErrors) {
                html += '<div class="alert alert-danger alert-sm" style="margin-bottom: 15px;">';
                html += '<strong>验证错误：</strong>';
                html += '<ul style="margin: 5px 0 0 20px;">';
                for (var j = 0; j < deal.validationErrors.length; j++) {
                    html += '<li>' + deal.validationErrors[j] + '</li>';
                }
                html += '</ul>';
                html += '</div>';
            }

            // ASIN详情表格
            if (asinCount > 0) {
                html += renderAsinTable(deal);
            } else {
                html += '<div class="text-center text-muted" style="padding: 20px;">';
                html += '<i class="fa fa-cube" style="font-size: 24px; color: #ddd;"></i>';
                html += '<p>暂无ASIN数据</p>';
                html += '</div>';
            }

            html += '</div>';
            html += '</div>';

            return html;
        }
        function getEventTypeText(eventType) {
            switch (eventType) {
                case 1: return '自定义日期';
                case 2: return '会员日';
                case 3: return '黑五';
            }
        }

        // 渲染ASIN详情表格
        function renderAsinTable(deal) {
            var html = '<div class="table-responsive">';
            html += '<table class="table table-striped table-bordered table-hover">';
            html += '<thead>';
            html += '<tr>';
            html += '<th width="8%">主键ID</th>';
            html += '<th width="12%">ASIN</th>';
            html += '<th width="15%">商品编码</th>';
            html += '<th width="18%">标题</th>';
            html += '<th width="8%">当前售价</th>';
            html += '<th width="8%">Reference price</th>';
            html += '<th width="10%">Deal price</th>';
            html += '<th width="8%">Committed units</th>';
            html += '<th width="10%">Per unit funding</th>';
            html += '<th width="7%">Discount</th>';
            html += '</tr>';
            html += '</thead>';
            html += '<tbody>';

            for (var i = 0; i < deal.asinList.length; i++) {
                var asin = deal.asinList[i];
                html += '<tr>';
                html += '<td>' + (asin.id || '-') + '</td>';
                html += '<td>' + (asin.platformGoodsId || '-') + '</td>';
                html += '<td>' + (asin.platformGoodsCode || '-') + '</td>';
                html += '<td title="' + (asin.title || '') + '">';
                html += (asin.title && asin.title.length > 30 ? asin.title.substring(0, 30) + '...' : (asin.title || '-'));
                html += '</td>';
                html += '<td>' + (asin.standardPrice ? parseFloat(asin.standardPrice).toFixed(2) : '-') + '</td>';
                html += '<td>' + renderReferencePriceCell(asin) + '</td>';
                html += '<td>' + renderPromotionPriceCell(asin, deal.id, i) + '</td>';
                html += '<td>' + renderCommittedUnitsCell(asin, deal.id, i) + '</td>';
                html += '<td>' + renderFundingCell(asin, deal.id, i) + '</td>';
                html += '<td>' + renderDiscountCell(asin, deal.id, i) + '</td>';
                html += '</tr>';
            }

            html += '</tbody>';
            html += '</table>';
            html += '</div>';

            return html;
        }

        // 渲染参考价单元格
        function renderReferencePriceCell(asin) {
            if (asin.referencePrice) {
                return '$' + parseFloat(asin.referencePrice).toFixed(2);
            } else {
                return '<span class="text-danger">No reference price</span>';
            }
        }

        // 渲染促销价单元格（上方输入框，下方Max显示）
        function renderPromotionPriceCell(asin, dealId, index) {
            if (!asin.referencePrice) {
                // 无参考价时显示禁用状态
                return '<div class="price-cell"><span class="text-muted">Cannot set</span></div>';
            }

            var promotionPrice = parseFloat(asin.promotionPrice) || parseFloat(asin.referencePrice) || 0;
            var maxPrice = parseFloat(asin.maxPrice) || 0;

            var html = '<div class="price-cell">';
            html += '<input type="number" class="form-control input-sm promotion-price-input" ';
            html += 'value="' + promotionPrice.toFixed(2) + '" ';
            html += 'data-deal-id="' + dealId + '" data-asin-index="' + index + '" ';
            html += 'onchange="updatePromotionPrice(this)" step="0.01" min="0" />';
            html += '<small class="text-muted max-price">Max: $' + maxPrice.toFixed(2) + '</small>';
            html += '</div>';

            return html;
        }

        // 渲染折扣单元格（显示实际折扣百分比）
        function renderDiscountCell(asin, dealId, index) {
            if (!asin.referencePrice) {
                return '<span class="text-muted">-</span>';
            }

            var referencePrice = parseFloat(asin.referencePrice) || 0;
            var promotionPrice = parseFloat(asin.promotionPrice) || 0;

            // 计算实际折扣百分比
            var discountPercent = 0;
            if (referencePrice > 0) {
                discountPercent = ((referencePrice - promotionPrice) / referencePrice) * 100;
            }

            var html = '<span class="discount-display" ';
            html += 'data-deal-id="' + dealId + '" data-asin-index="' + index + '">';
            html += discountPercent.toFixed(1) + '%';
            html += '</span>';

            return html;
        }

        // 渲染承诺数量单元格
        function renderCommittedUnitsCell(asin, dealId, index) {
            if (!asin.referencePrice) {
                // 无参考价时显示禁用状态
                return '<div class="price-cell"><span class="text-muted">Cannot set</span></div>';
            }
            var committedUnits = asin.committedUnits  || 1;

            var html = '<div class="committed-units-cell">';
            html += '<input type="number" class="form-control input-sm committed-units-input" ';
            html += 'value="' + committedUnits + '" ';
            html += 'data-deal-id="' + dealId + '" data-asin-index="' + index + '" ';
            html += 'onchange="updateCommittedUnits(this)" min="1" step="1" ';
            html += 'placeholder="Committed units" />';
            html += '</div>';

            return html;
        }

        // 渲染funding单元格（上方输入框，下方Min显示）
        function renderFundingCell(asin, dealId, index) {
            if (!asin.referencePrice) {
                return '<div class="price-cell"><span class="text-muted">Cannot set</span></div>';
            }

            var funding = parseFloat(asin.funding) || 0;
            var minFunding = parseFloat(asin.minFunding) || 0;

            var html = '<div class="price-cell">';
            html += '<input type="number" class="form-control input-sm funding-input" ';
            html += 'value="' + funding.toFixed(2) + '" ';
            html += 'data-deal-id="' + dealId + '" data-asin-index="' + index + '" ';
            html += 'onchange="updateFunding(this)" step="0.01" min="0" />';
            html += '<small class="text-muted min-funding">Min: $' + minFunding.toFixed(2) + '</small>';
            html += '</div>';

            return html;
        }

        // 切换促销记录详情显示/隐藏
        function toggleDealDetails(dealId) {
            // 使用更精确的选择器，确保选择正确的元素
            var dealCard = $('[data-deal-id="' + dealId + '"]');
            var details = dealCard.find('#details-' + dealId);
            var icon = dealCard.find('#toggle-' + dealId);

            // 如果找不到元素，尝试全局查找
            if (details.length === 0) {
                details = $('#details-' + dealId);
            }
            if (icon.length === 0) {
                icon = $('#toggle-' + dealId);
            }

            // 找到对应的deal对象
            var deal = findDealById(dealId);
            if (!deal) {
                console.error('找不到deal:', dealId);
                return;
            }

            if (details.is(':visible')) {
                details.slideUp();
                icon.removeClass('fa-chevron-up').addClass('fa-chevron-down');
                deal.isCollapsed = true; // 保存折叠状态
            } else {
                details.slideDown();
                icon.removeClass('fa-chevron-down').addClass('fa-chevron-up');
                deal.isCollapsed = false; // 保存展开状态
            }

        }

        // 删除BD促销记录
        function removeDeal(dealId) {
            $.modal.confirm("确认要删除这个BD促销记录吗？", function() {
                dealList = dealList.filter(function(deal) {
                    return deal.id !== dealId;
                });

                renderDealList();
                updateWorkspaceStats();
            });
        }

        // 查找BD促销记录
        function findDealById(dealId) {
            return dealList.find(function(deal) {
                return deal.id === dealId;
            });
        }

        // 获取刊登类型文本
        function getPublishTypeText(publishType) {
            switch (publishType) {
                case '5': return 'VCDF';
                case '6': return 'VCPO';
                default: return '未设置';
            }
        }

        // 获取默认折扣文本
        function getDefaultDiscountText(eventType) {
            switch (eventType) {
                case 1: return '10%'; // 自定义日期
                case 2: return '15%'; // 会员日
                case 3: return '15%'; // 黑五
                default: return '10%'; // 默认
            }
        }

        // 获取默认折扣数值（用于后端提交）
        function getDefaultDiscountByEventType(eventType) {
            switch (eventType) {
                case 1: return 10; // 自定义日期
                case 2: return 15; // 会员日
                case 3: return 15; // 黑五
                default: return 10; // 默认
            }
        }

        // 获取默认折扣比例（用于前端计算）
        function getDefaultDiscountRateByEventType(eventType) {
            switch (eventType) {
                case 1: return 10; // 自定义日期：10%
                case 2: return 15; // 会员日：15%
                case 3: return 15; // 黑五：15%
                default: return 10; // 默认：10%
            }
        }

        // 更新工作区统计
        function updateWorkspaceStats() {
            var totalDeals = dealList.length;
            var readyDeals = dealList.filter(function(deal) {
                return deal.status === 'ready';
            }).length;
            var totalAsins = dealList.reduce(function(sum, deal) {
                return sum + (deal.asinList ? deal.asinList.length : 0);
            }, 0);

            $('#total-deals').text(totalDeals);
            $('#ready-deals').text(readyDeals);
            $('#total-asins').text(totalAsins);
            $('#submit-count').text(readyDeals);

            // 更新提交按钮状态
            $('#submit-btn').prop('disabled', readyDeals === 0);
        }

        // 更新列表摘要
        function updateListSummary() {
            var totalDeals = dealList.length;
            var summary = totalDeals + ' 条记录';
            $('#list-summary').text(summary);
        }

        // 批量提交BD促销记录
        function submitBatch() {
            // 获取就绪的记录（已在confirmAction中完成校验）
            var readyDeals = dealList.filter(function(deal) {
                return deal.status === 'ready';
            });

            // 直接提交（校验已在confirmAction中完成）
            performBatchSubmit(readyDeals);
        }

        // 执行批量提交
        function performBatchSubmit(readyDeals) {
            // 显示提交进度
            var submitBtn = $('#submit-btn');
            var originalText = submitBtn.html();
            submitBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 提交中...');

            // 准备提交数据，匹配后端AmBestDealRecord结构
            var submitData = readyDeals.map(function(deal) {
                var eventType = parseInt(deal.eventType) || 1;
                var recordData = {
                    // AmBestDealRecord字段
                    promotionName: deal.promotionName,
                    promotionId: deal.promotionId || null,
                    site: deal.site,
                    publishType: parseInt(deal.publishType), // 确保是Integer类型
                    status: 'DRAFT', // 提交时状态设为草稿
                    dealType: 1, // 1:BD,2:LD - 固定为BD
                    eventType: eventType, // 1:自定义日期，2：会员日，3：黑五
                    remark: deal.remark || null
                };

                // 只有自定义日期类型才添加时间字段
                if (eventType === 1) {
                    recordData.startDateUtc = deal.startDateUtc;
                    recordData.endDateUtc = deal.endDateUtc;
                }

                // 添加AmBestDealAsin列表
                recordData.asinList = deal.asinList.map(function(asin) {
                        // 计算折扣百分比
                        var referencePrice = parseFloat(asin.referencePrice) || 0;
                        var promotionPrice = parseFloat(asin.promotionPrice) || 0;
                        var actualDiscount = referencePrice > 0 ?
                            Math.round(((referencePrice - promotionPrice) / referencePrice) * 100) : 0;

                        // 根据事件类型确定默认折扣
                        var defaultDiscount = getDefaultDiscountByEventType(parseInt(deal.eventType) || 1);

                        return {
                            // AmBestDealAsin字段
                            headId: asin.id,
                            platformGoodsId: asin.platformGoodsId,
                            platformGoodsCode: asin.platformGoodsCode,
                            standardPrice: parseFloat(asin.standardPrice) || null,
                            referencePrice: parseFloat(asin.referencePrice) || 0,
                            dealPrice: parseFloat(asin.promotionPrice) || 0, // 促销价对应dealPrice
                            perUnitFunding: parseFloat(asin.funding) || 0, // funding对应perUnitFunding
                            committedUnits: parseInt(asin.committedUnits)  || 0, // 优先使用用户设置的承诺数量
                            lowestDiscount: defaultDiscount, // 根据事件类型设置默认折扣
                            actualDiscount: actualDiscount, // 计算实际折扣
                            expectedDemand:  null // 预期需求量
                        };
                    });

                return recordData;
            });

            // 调用后端API
            $.ajax({
                url: prefix + "/addBatch",
                type: "POST",
                contentType: "application/json",
                dataType: "json",
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍候...");
                },
                data: JSON.stringify(submitData),
                success: function(e) {
                    hideLoadingState();
                    let listingPrefix = ctx + "promotion/record";
                    if (e.code === 0) {
                        top.layer.confirm("生成草稿成功，请移至Best Deal管理页面！", {
                            icon: 3,
                            title: "系统提示",
                            btn: ['确认', '取消']
                        }, function (index) {
                            $.modal.close(index);
                            $.modal.closeTab();
                            $.modal.openTab('Best Deal', listingPrefix);
                        }, function () {
                            $.modal.closeTab();
                        });
                    } else {
                        if (e.msg == null || e.msg == '') {
                            $.modal.alertError("批量生成草稿失败,请重新检查数据或联系管理员.");
                        } else {
                            $.modal.alertError(e.msg);
                        }
                        $.modal.closeLoading();
                    }
                },
                error: function(xhr, status, error) {
                    $.modal.closeLoading();
                    $.modal.alertError('批量提交失败');
                }
            });
        }

        // 浮点数精度比较函数
        function floatEquals(a, b, precision) {
            precision = precision || 0.01; // 默认精度为0.01（分）
            return Math.abs(a - b) < precision;
        }

        function floatLessThan(a, b, precision) {
            precision = precision || 0.01;
            return (a - b) < -precision; // a 明显小于 b
        }

        function floatGreaterThan(a, b, precision) {
            precision = precision || 0.01;
            return (a - b) > precision; // a 明显大于 b
        }

        function floatLessOrEqual(a, b, precision) {
            precision = precision || 0.01;
            return (a - b) <= precision; // a 小于等于 b（考虑精度）
        }

        function floatGreaterOrEqual(a, b, precision) {
            precision = precision || 0.01;
            return (a - b) >= -precision; // a 大于等于 b（考虑精度）
        }

        // 计算最高优惠价（Max = 参考价 - funding）
        function calculateMaxPrice(asin) {
            var referencePrice = parseFloat(asin.referencePrice) || 0;
            var funding = parseFloat(asin.funding) || (referencePrice * 0.1);
            return (Math.max(0, referencePrice - funding)).toFixed(2);
        }

        // 计算最小优惠funding（Min = 参考价 × 10%）- 保持原有函数兼容性
        function calculateMinFunding(asin) {
            var referencePrice = parseFloat(asin.referencePrice) || 0;
            return referencePrice * 0.1;
        }

        // 根据事件类型计算最小优惠funding
        function calculateMinFundingByEventType(asin, eventType) {
            var referencePrice = parseFloat(asin.referencePrice) || 0;
            var discountRate = getDefaultDiscountRateByEventType(parseInt(eventType) || 1);
            return referencePrice * discountRate / 100;
        }

        // 计算80%funding上限（funding不能超过此值）
        function calculateMaxAllowedFunding(referencePrice) {
            return parseFloat(referencePrice) * 0.8;
        }

        // 计算促销价下限（促销价不能低于此值，即参考价的20%）
        function calculateMinAllowedPrice(referencePrice) {
            return parseFloat(referencePrice) * 0.2;
        }

        // 更新折扣显示
        function updateDiscountDisplay(dealId, asinIndex) {
            var deal = findDealById(dealId);
            if (deal && deal.asinList && deal.asinList[asinIndex]) {
                var asin = deal.asinList[asinIndex];
                var referencePrice = parseFloat(asin.referencePrice) || 0;
                var promotionPrice = parseFloat(asin.promotionPrice) || 0;

                // 计算实际折扣百分比
                var discountPercent = 0;
                if (referencePrice > 0) {
                    discountPercent = ((referencePrice - promotionPrice) / referencePrice) * 100;
                }

                // 更新显示
                var discountElement = $('[data-deal-id="' + dealId + '"][data-asin-index="' + asinIndex + '"].discount-display');
                discountElement.text(discountPercent.toFixed(1) + '%');
            }
        }

        // 更新促销价
        function updatePromotionPrice(input) {
            var dealId = $(input).data('deal-id');
            var asinIndex = $(input).data('asin-index');
            var newPrice = parseFloat($(input).val()) || 0;

            var deal = findDealById(dealId);
            if (deal && deal.asinList && deal.asinList[asinIndex]) {
                var asin = deal.asinList[asinIndex];
                var referencePrice = parseFloat(asin.referencePrice) || 0;
                var minFunding = parseFloat(asin.minFunding) || 0;
                var maxPrice = parseFloat(asin.maxPrice) || 0; // 接口返回的最高限价
                var maxAllowedFunding = calculateMaxAllowedFunding(referencePrice); // 80%funding上限
                var minAllowedPrice = calculateMinAllowedPrice(referencePrice); // 促销价下限（20%）



                // 校验促销价不能低于下限（参考价的20%）（使用精度比较）
                if (floatLessThan(newPrice, minAllowedPrice)) {
                    // 在输入框下方显示提示信息
                    var errorMsg = 'Deal price cannot be lower than $' + minAllowedPrice.toFixed(2) + ' (20% of reference price)';
                    showInputError($(input), errorMsg);
                    $(input).val(asin.promotionPrice || '');

                    return;
                }

                // 校验促销价不能超过接口返回的最高限价（使用精度比较）
                if (floatGreaterThan(newPrice, maxPrice)) {
                    var errorMsg = 'Deal price cannot exceed maximum limit $' + maxPrice.toFixed(2);
                    showInputError($(input), errorMsg);
                    $(input).val(asin.promotionPrice || '');
                    return;
                }

                // 根据促销价计算对应的funding：funding = referencePrice - promotionPrice
                var calculatedFunding = referencePrice - newPrice;


                // 校验计算出的funding不能低于最小值（使用精度比较）
                if (floatLessThan(calculatedFunding, minFunding)) {
                    var maxAllowedPrice = referencePrice - minFunding;
                    var errorMsg = 'Deal price too high, would result in funding below minimum. Maximum deal price: $' + maxAllowedPrice.toFixed(2);
                    showInputError($(input), errorMsg);
                    $(input).val(asin.promotionPrice || '');

                    return;
                }

                // 校验计算出的funding不能超过80%上限（使用精度比较）
                if (floatGreaterThan(calculatedFunding, maxAllowedFunding)) {
                    var minAllowedPriceForFunding = referencePrice - maxAllowedFunding;
                    var errorMsg = 'Deal price too low, would result in funding exceeding 80% limit. Minimum deal price: $' + minAllowedPriceForFunding.toFixed(2);
                    showInputError($(input), errorMsg);
                    $(input).val(asin.promotionPrice || '');
                    return;
                }

                // 清除可能的错误提示
                clearInputError($(input));

                // 更新促销价和对应的funding（保留两位小数）
                asin.promotionPrice = parseFloat(newPrice.toFixed(2));
                asin.funding = parseFloat(calculatedFunding.toFixed(2));

                // 同步更新funding输入框的值
                var fundingInput = $(input).closest('tr').find('.funding-input');
                fundingInput.val(asin.funding.toFixed(2));

                // 更新折扣显示
                updateDiscountDisplay(dealId, asinIndex);
            }
        }

        // 显示输入框错误提示
        function showInputError(inputElement, message) {
            // 移除之前的错误提示
            clearInputError(inputElement);

            // 添加错误样式
            inputElement.addClass('is-invalid');

            // 在输入框下方添加错误提示
            var errorDiv = $('<div class="invalid-feedback" style="display: block; font-size: 12px; color: #dc3545;">' + message + '</div>');
            inputElement.closest('.price-cell').append(errorDiv);
        }

        // 清除输入框错误提示
        function clearInputError(inputElement) {
            inputElement.removeClass('is-invalid');
            inputElement.closest('.price-cell').find('.invalid-feedback').remove();
        }

        // 更新承诺数量
        function updateCommittedUnits(input) {
            var dealId = $(input).data('deal-id');
            var asinIndex = $(input).data('asin-index');
            var newCommittedUnits = parseInt($(input).val()) || 0;

            var deal = findDealById(dealId);
            if (deal && deal.asinList && deal.asinList[asinIndex]) {
                var asin = deal.asinList[asinIndex];
                asin.committedUnits = newCommittedUnits;
            }
        }

        // 更新funding
        function updateFunding(input) {
            var dealId = $(input).data('deal-id');
            var asinIndex = $(input).data('asin-index');
            var newFunding = parseFloat($(input).val()) || 0;

            var deal = findDealById(dealId);
            if (deal && deal.asinList && deal.asinList[asinIndex]) {
                var asin = deal.asinList[asinIndex];
                var referencePrice = parseFloat(asin.referencePrice) || 0;
                var minFunding = parseFloat(asin.minFunding) || 0;
                var maxPrice = parseFloat(asin.maxPrice) || 0; // 接口返回的最高限价
                var maxAllowedFunding = calculateMaxAllowedFunding(referencePrice); // 80%funding上限
                var minAllowedPrice = calculateMinAllowedPrice(referencePrice); // 促销价下限（20%）

                // 校验funding不能低于最小值（使用精度比较）
                if (floatLessThan(newFunding, minFunding)) {
                    var errorMsg = 'Per unit funding cannot be lower than minimum $' + minFunding.toFixed(2);
                    showInputError($(input), errorMsg);
                    $(input).val(asin.funding || minFunding);
                    return;
                }

                // 校验funding不能超过80%上限（使用精度比较）
                if (floatGreaterThan(newFunding, maxAllowedFunding)) {
                    var errorMsg = 'Per unit funding cannot exceed $' + maxAllowedFunding.toFixed(2) + ' (80% of reference price)';
                    showInputError($(input), errorMsg);
                    $(input).val(asin.funding || '');
                    return;
                }

                // 根据新的funding计算对应的促销价：promotionPrice = referencePrice - funding
                var calculatedPromotionPrice = referencePrice - newFunding;

                // 校验计算出的促销价不能低于下限（参考价的20%）（使用精度比较）
                if (floatLessThan(calculatedPromotionPrice, minAllowedPrice)) {
                    var maxAllowedFundingForPrice = referencePrice - minAllowedPrice;
                    var errorMsg = 'Per unit funding too high, would result in deal price below 20% limit. Maximum funding: $' + maxAllowedFundingForPrice.toFixed(2);
                    showInputError($(input), errorMsg);
                    $(input).val(asin.funding || '');
                    return;
                }

                // 校验计算出的促销价不能超过接口返回的最高限价（使用精度比较）
                if (floatGreaterThan(calculatedPromotionPrice, maxPrice)) {
                    var maxAllowedFunding = referencePrice - maxPrice;
                    var errorMsg = 'Per unit funding too low, would result in deal price exceeding maximum limit. Minimum funding: $' + maxAllowedFunding.toFixed(2);
                    showInputError($(input), errorMsg);
                    $(input).val(asin.funding || '');
                    return;
                }

                // 清除可能的错误提示
                clearInputError($(input));

                // 更新funding和对应的促销价（保留两位小数）
                asin.funding = parseFloat(newFunding.toFixed(2));
                asin.promotionPrice = parseFloat(calculatedPromotionPrice.toFixed(2));

                // 同步更新促销价输入框的值
                var promotionPriceInput = $(input).closest('tr').find('.promotion-price-input');
                promotionPriceInput.val(asin.promotionPrice.toFixed(2));
                promotionPriceInput.removeClass('is-invalid'); // 移除可能的错误状态

                // 更新折扣显示
                updateDiscountDisplay(dealId, asinIndex);
            }
        }

        // 确认操作（适配标签页模式）
        function confirmAction() {
            // 1. 首先进行完整的表单校验
            var validationResult = validateAllDeals();
            if (!validationResult.isValid) {
                // 页面内部已经显示了详细错误信息，弹窗只做简单提示
                var errorCount = validationResult.errors.length;
                var errorRecordCount = dealList.filter(function(deal) { return deal.status === 'error'; }).length;

                $.modal.alertWarning("发现 " + errorRecordCount + " 条记录存在校验问题，请查看下方详细错误信息并修正后重试。");

                // 自动滚动到第一个错误记录
                scrollToFirstError();
                return;
            }

            // 2. 检查是否有就绪的记录
            var readyDeals = dealList.filter(function(deal) {
                return deal.status === 'ready';
            });

            if (readyDeals.length === 0) {
                $.modal.alertWarning("没有就绪的BD促销记录可以提交");
                return;
            }

            // 3. 最终确认提交
            $.modal.confirm("确认要提交 " + readyDeals.length + " 条BD促销记录吗？", function() {
                // 显示加载中状态
                showLoadingState();

                // 调用批量提交功能
                submitBatch();
            });
        }

        // 滚动到第一个错误记录
        function scrollToFirstError() {
            var firstErrorDeal = dealList.find(function(deal) {
                return deal.status === 'error';
            });

            if (firstErrorDeal) {
                var errorElement = $('#deal-card-' + firstErrorDeal.id);
                if (errorElement.length > 0) {
                    // 滚动到错误记录
                    $('html, body').animate({
                        scrollTop: errorElement.offset().top - 100
                    }, 500);

                    // 高亮显示错误记录
                    errorElement.addClass('highlight-error');
                    setTimeout(function() {
                        errorElement.removeClass('highlight-error');
                    }, 3000);
                }
            }
        }

        // 验证所有BD促销记录（提交时使用完整校验）
        function validateAllDeals() {
            var allErrors = [];
            var hasValidDeals = false;

            // 检查是否有记录
            if (!dealList || dealList.length === 0) {
                allErrors.push("请至少添加一条BD促销记录");
                return { isValid: false, errors: allErrors };
            }

            // 逐个验证每条记录（使用完整校验）
            for (var i = 0; i < dealList.length; i++) {
                var deal = dealList[i];

                // 使用完整校验
                var newStatus = validateDealComplete(deal);
                deal.status = newStatus;

                if (newStatus === 'ready') {
                    hasValidDeals = true;
                } else if (newStatus === 'error' && deal.validationErrors) {
                    allErrors.push("第" + (i + 1) + "条记录：" + deal.validationErrors.join(', '));
                }
            }

            // 检查是否有可提交的记录
            if (!hasValidDeals) {
                allErrors.push("没有通过校验的记录可以提交");
            }

            // 更新界面显示
            renderDealList();
            updateWorkspaceStats();

            return {
                isValid: allErrors.length === 0,
                errors: allErrors
            };
        }

        // 显示加载中状态
        function showLoadingState() {
            // 禁用所有按钮
            $('.btn').prop('disabled', true);

            // 更新确认按钮显示
            var confirmBtn = $('button[onclick="confirmAction()"]');
            confirmBtn.html('<i class="fa fa-spinner fa-spin"></i> 提交中...');

            // 显示全局加载遮罩
            var loadingHtml = '<div id="page-loading" class="loading-overlay">';
            loadingHtml += '<div class="loading-content">';
            loadingHtml += '<div class="loading-spinner"><i class="fa fa-spinner fa-spin"></i></div>';
            loadingHtml += '<p class="loading-text">正在提交BD促销记录...</p>';
            loadingHtml += '<p class="loading-text" style="font-size: 14px; color: #999; margin-top: 10px;">请稍候，不要关闭页面</p>';
            loadingHtml += '</div></div>';
            $('body').append(loadingHtml);
        }

        // 隐藏加载中状态
        function hideLoadingState() {
            // 移除加载遮罩
            $('#page-loading').remove();

            // 恢复按钮状态
            $('.btn').prop('disabled', false);

            // 恢复确认按钮显示
            var confirmBtn = $('button[onclick="confirmAction()"]');
            confirmBtn.html('<i class="fa fa-check"></i> 确认');
        }


        // 关闭当前标签页
        function closeCurrentTab() {
            try {
                // 尝试关闭当前标签页
                if (window.parent && window.parent !== window) {
                    // 如果是在iframe中，通知父页面关闭
                    if (window.parent.$.modal && typeof window.parent.$.modal.close === 'function') {
                        window.parent.$.modal.close();
                    } else if (window.parent.layer && typeof window.parent.layer.closeAll === 'function') {
                        window.parent.layer.closeAll();
                    }
                } else {
                    // 如果是独立页面，返回列表页
                    window.location.href = prefix;
                }
            } catch (e) {
                console.error("关闭标签页失败:", e);
                // 降级处理：返回列表页
                window.location.href = prefix;
            }
        }

        // 取消操作（适配标签页模式）
        function cancelAction() {
            $.modal.confirm("确认要取消当前操作吗？", function() {
                // 关闭当前标签页
                closeCurrentTab();
            });
        }
    </script>
</body>
</html>
