package com.suncent.smc.provider.biz.promotion;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.suncent.smc.common.exception.BusinessException;
import com.suncent.smc.persistence.promotion.domain.entity.AmBestDealAsin;
import com.suncent.smc.persistence.promotion.domain.entity.AmBestDealRecord;
import com.suncent.smc.persistence.promotion.domain.entity.AmPromotionRpaTask;
import com.suncent.smc.persistence.promotion.service.IAmBestDealAsinService;
import com.suncent.smc.persistence.promotion.service.IAmBestDealRecordService;
import com.suncent.smc.persistence.promotion.service.IAmPromotionRpaTaskService;
import com.suncent.smc.provider.biz.promotion.dto.BdActivityDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 亚马逊VC促销活动业务处理类
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Component
public class AmazonVcPromotionBiz {

    @Autowired
    private IAmBestDealRecordService amBestDealRecordService;

    @Autowired
    private IAmBestDealAsinService amBestDealAsinService;

    @Autowired
    private IAmPromotionRpaTaskService amPromotionRpaTaskService;

    /**
     * 根据promotion_id构建亚马逊VC促销活动JSON（简化格式）
     * 新增活动使用
     *
     * @param recordId AmBestDealRecord的主键ID
     * @return 促销活动JSON字符串
     */
    public String buildAddBDPromotionJson(Long recordId) {
        if (recordId == null) {
            throw new BusinessException("记录ID不能为空");
        }

        // 1. 查询促销记录
        AmBestDealRecord promotionRecord = amBestDealRecordService.selectAmBestDealRecordById(recordId);
        if (promotionRecord == null) {
            throw new BusinessException("未找到促销记录，记录ID: " + recordId);
        }

        // 2. 查询关联的ASIN列表
        List<AmBestDealAsin> asinList = amBestDealAsinService.selectAmBestDealAsinByRefId(promotionRecord.getId());
        if (CollUtil.isEmpty(asinList)) {
            throw new BusinessException("未找到关联的ASIN记录，记录ID: " + recordId);
        }

        // 3. 构建简化的促销活动JSON
        Map<String, Object> result = buildSimplifiedPromotionData(promotionRecord, asinList);

        // 4. 转换为JSON字符串
        String jsonResult = JSONUtil.toJsonPrettyStr(result);

        log.info("成功构建促销活动JSON，记录ID: {}, 促销ID: {}, ASIN数量: {}",
                recordId, promotionRecord.getPromotionId(), asinList.size());

        return jsonResult;
    }

    /**
     * 保存修改BD活动
     * 比较新旧数据，生成对应的新增或更新JSON，并写入RPA任务表
     *
     * @param newActivityData 新的BD活动数据
     * @param oldActivityData 旧的BD活动数据（可为null，表示新增）
     * @return 操作结果
     */
    public String saveModifyBdActivity(BdActivityDTO newActivityData, BdActivityDTO oldActivityData) {
        if (newActivityData == null || newActivityData.getActivityRecord() == null) {
            throw new BusinessException("新活动数据不能为空");
        }

        try {
            // 设置操作信息
            if (newActivityData.getOperationInfo() == null) {
                throw new BusinessException("操作信息不能为空");
            }

            // 判断是新增还是更新操作
            boolean isNewPromotion = newActivityData.isNewActivity() ||
                    (oldActivityData == null || oldActivityData.isNewActivity());

            String operationType;
            String executeJson;
            String promotionId = newActivityData.getActivityRecord().getPromotionId();
            String operatorId = newActivityData.getOperationInfo().getOperatorId();

            if (isNewPromotion) {
                // 新增操作 - 使用AmBestDealRecord的主键ID
                Long recordId = newActivityData.getActivityRecord().getId();
                if (recordId == null) {
                    throw new BusinessException("新活动的AmBestDealRecord主键ID不能为空");
                }

                operationType = AmPromotionRpaTask.OperationType.ADD.getCode();

                log.info("开始构建新增BD活动JSON，记录ID: {}", recordId);
                try {
                    executeJson = this.buildAddBDPromotionJson(recordId);
                    log.info("成功构建新增BD活动JSON，记录ID: {}", recordId);
                } catch (Exception e) {
                    log.error("构建新增BD活动JSON失败，记录ID: {}", recordId, e);
                    throw e;
                }

                log.info("创建新增BD活动任务，记录ID: {}, 促销ID: {}, 操作人: {}", recordId, promotionId, operatorId);

                // 更新统计信息
                int totalAsins = newActivityData.getAsinList() != null ? newActivityData.getAsinList().size() : 0;
                newActivityData.updateChangeStatistics(totalAsins, 0, 0, true);
            } else {
                // 更新操作 - 比较新旧数据
                BdActivityComparisonResult comparisonResult = compareBdActivityData(newActivityData, oldActivityData);

                if (comparisonResult.hasChanges()) {
                    operationType = AmPromotionRpaTask.OperationType.UPDATE.getCode();
                    executeJson = this.buildUpdatePromotionJson(promotionId);
                    log.info("创建更新BD活动任务，促销ID: {}, 操作人: {}, 变更统计: 新增{}个, 修改{}个, 删除{}个",
                            promotionId, operatorId,
                            comparisonResult.getAddedAsins().size(),
                            comparisonResult.getModifiedAsins().size(),
                            comparisonResult.getRemovedAsins().size());

                    // 更新统计信息
                    newActivityData.updateChangeStatistics(
                            comparisonResult.getAddedAsins().size(),
                            comparisonResult.getModifiedAsins().size(),
                            comparisonResult.getRemovedAsins().size(),
                            comparisonResult.isActivityInfoChanged()
                    );
                } else {
                    log.info("BD活动数据无变更，无需创建RPA任务，促销ID: {}, 操作人: {}", promotionId, operatorId);
                    newActivityData.updateChangeStatistics(0, 0, 0, false);
                    return "数据无变更，无需创建任务";
                }
            }

            // 创建 BD活动类型 RPA任务
            int result = amPromotionRpaTaskService.createPromotionRpaTask(
                    newActivityData.getActivityRecord().getId(),
                    operationType,
                    promotionId,
                    newActivityData.getActivityRecord().getPublishType(),
                    newActivityData.getActivityRecord().getSite(),
                    1,
                    executeJson,
                    operatorId
            );

            if (result > 0) {
                String message = isNewPromotion ? "新增BD活动任务创建成功" : "更新BD活动任务创建成功";
                log.info("{}, 促销ID: {}, 操作人: {}", message, promotionId, operatorId);
                return message;
            } else {
                throw new BusinessException("RPA任务创建失败");
            }

        } catch (Exception e) {
            String promotionId = newActivityData.getActivityRecord() != null ?
                    newActivityData.getActivityRecord().getPromotionId() : "未知";
            log.error("保存修改BD活动失败，促销ID: {}", promotionId, e);

            // 获取更详细的错误信息
            String errorMessage = e.getMessage();
            if (errorMessage == null || errorMessage.trim().isEmpty()) {
                errorMessage = e.getClass().getSimpleName();
                if (e.getCause() != null) {
                    String causeMessage = e.getCause().getMessage();
                    if (causeMessage != null && !causeMessage.trim().isEmpty()) {
                        errorMessage += ": " + causeMessage;
                    } else {
                        errorMessage += ": " + e.getCause().getClass().getSimpleName();
                    }
                }
            }

            throw new BusinessException("保存修改BD活动失败: " + errorMessage);
        }
    }

    /**
     * 批量确认BD促销记录
     * 将草稿状态的记录更新为UPDATING状态，并创建RPA任务
     *
     * @param ids 记录ID数组
     * @param operatorId 操作人ID
     * @return 成功确认的记录数量
     */
    @Transactional(rollbackFor = Exception.class)
    public int batchConfirmBdPromotions(String[] ids, String operatorId) {
        if (ids == null || ids.length == 0) {
            throw new BusinessException("记录ID不能为空");
        }
        if (StrUtil.isBlank(operatorId)) {
            throw new BusinessException("操作人ID不能为空");
        }

        log.info("开始批量确认BD促销记录，记录数量: {}, 操作人: {}", ids.length, operatorId);

        int successCount = 0;
        List<String> errorMessages = new ArrayList<>();

        try {
            for (String idStr : ids) {
                Long id = Long.parseLong(idStr);

                // 1. 查询记录并验证状态
                AmBestDealRecord record = amBestDealRecordService.selectAmBestDealRecordById(id);
                if (record == null) {
                    errorMessages.add("记录不存在，ID: " + id);
                    continue;
                }

                // 只能确认草稿状态的记录
                if (!"DRAFT".equals(record.getStatus())) {
                    errorMessages.add("记录状态不是草稿，无法确认，ID: " + id + ", 当前状态: " + record.getStatus());
                    continue;
                }
                // 2. 更新状态为UPDATING
                String oldStatus = record.getStatus();
                int updateResult = amBestDealRecordService.updateAmBestDealRecordStatus(id, "UPDATING");
                if (updateResult <= 0) {
                    errorMessages.add("更新状态失败，ID: " + id);
                    continue;
                }

                // 3. 构建BdActivityDTO并创建RPA任务
                try {
                    // 查询关联的ASIN列表
                    List<AmBestDealAsin> asinList = amBestDealAsinService.selectAmBestDealAsinByRefId(id);
                    record.setAsinList(asinList);

                    // 构建BdActivityDTO
                    BdActivityDTO activityData = new BdActivityDTO();
                    activityData.setActivityRecord(record);
                    activityData.setAsinList(asinList);

                    // 设置操作信息
                    BdActivityDTO.OperationInfo operationInfo = new BdActivityDTO.OperationInfo();
                    operationInfo.setOperatorId(operatorId);
                    activityData.setOperationInfo(operationInfo);

                    // 调用saveModifyBdActivity创建RPA任务（新增操作，oldActivityData为null）
                    String result = this.saveModifyBdActivity(activityData, null);

                    log.info("成功创建RPA任务，记录ID: {}, 促销ID: {}, 结果: {}",
                            id, record.getPromotionId(), result);

                    successCount++;

                } catch (Exception e) {
                    log.error("创建RPA任务失败，记录ID: {}, 促销ID: {}", id, record.getPromotionId(), e);
                    // RPA任务创建失败，需要回滚状态
                    amBestDealRecordService.updateAmBestDealRecordStatus(id, oldStatus);
                    errorMessages.add("创建RPA任务失败，ID: " + id + ", 错误: " + e.getMessage());
                }
            }

            // 如果有错误且没有成功的记录，抛出异常触发事务回滚
            if (successCount == 0 && !errorMessages.isEmpty()) {
                throw new BusinessException("批量确认失败: " + String.join("; ", errorMessages));
            }

            // 如果部分成功，记录警告日志但不回滚事务
            if (!errorMessages.isEmpty()) {
                log.warn("批量确认部分失败，成功: {}, 失败: {}, 错误信息: {}",
                        successCount, errorMessages.size(), String.join("; ", errorMessages));
            }

            log.info("批量确认BD促销记录完成，成功: {}, 失败: {}", successCount, errorMessages.size());
            return successCount;

        } catch (Exception e) {
            log.error("批量确认BD促销记录失败，操作人: {}", operatorId, e);
            throw new BusinessException("批量确认失败: " + e.getMessage());
        }
    }

    /**
     * 取消BD活动
     * 根据促销ID取消BD活动，并写入RPA任务表
     *
     * @param promotionId 促销ID
     * @param operatorId  操作人ID
     * @return 操作结果
     */
    public String cancelBdActivity(String promotionId, String operatorId) {
        if (StrUtil.isBlank(promotionId)) {
            throw new BusinessException("促销ID不能为空");
        }
        if (StrUtil.isBlank(operatorId)) {
            throw new BusinessException("操作人ID不能为空");
        }

        try {
            // 1. 查询促销记录
            AmBestDealRecord promotionRecord = amBestDealRecordService.selectAmBestDealRecordByPromotionId(promotionId);
            if (promotionRecord == null) {
                throw new BusinessException("未找到促销记录，促销ID: " + promotionId);
            }

            // 2. 取消操作使用空JSON，只需要在task表中记录删除标识和促销ID
            log.info("创建取消BD活动任务，促销ID: {}, 操作人: {}", promotionId, operatorId);

            // 3. 创建取消BD活动类型的RPA任务
            int result = amPromotionRpaTaskService.createPromotionRpaTask(
                    promotionRecord.getId(),
                    AmPromotionRpaTask.OperationType.DELETE.getCode(),
                    promotionId,
                    promotionRecord.getPublishType(),
                    promotionRecord.getSite(),
                    1,
                    null,
                    operatorId
            );

            if (result > 0) {
                String message = "取消BD活动任务创建成功";
                log.info("{}, 促销ID: {}, 操作人: {}", message, promotionId, operatorId);
                return message;
            } else {
                throw new BusinessException("RPA任务创建失败");
            }

        } catch (Exception e) {
            log.error("取消BD活动失败，促销ID: {}", promotionId, e);
            throw new BusinessException("取消BD活动失败: " + e.getMessage());
        }
    }

    /**
     * 批量取消BD促销记录
     * 将更新中状态的记录更新为CANCELED状态，并创建取消RPA任务
     *
     * @param ids 记录ID数组
     * @param operatorId 操作人ID
     * @return 成功取消的记录数量
     */
    @Transactional(rollbackFor = Exception.class)
    public int batchCancelBdPromotions(String[] ids, String operatorId) {
        if (ids == null || ids.length == 0) {
            throw new BusinessException("记录ID不能为空");
        }

        log.info("开始批量取消BD促销记录，记录数量: {}, 操作人: {}", ids.length, operatorId);

        int successCount = 0;
        List<String> errorMessages = new ArrayList<>();

        try {
            for (String idStr : ids) {
                Long id = Long.parseLong(idStr);

                // 1. 查询记录并验证状态
                AmBestDealRecord record = amBestDealRecordService.selectAmBestDealRecordById(id);
                if (record == null) {
                    errorMessages.add("记录不存在，ID: " + id);
                    continue;
                }

                // 只能取消更新中状态的记录
                if (!"UPDATING".equals(record.getStatus())) {
                    errorMessages.add("记录状态不是更新中，无法取消，ID: " + id + ", 当前状态: " + record.getStatus());
                    continue;
                }

                // 验证必要字段
                if (record.getPromotionId() == null || record.getPromotionId().trim().isEmpty()) {
                    errorMessages.add("促销ID为空，无法取消，ID: " + id);
                    continue;
                }

                // 2. 更新状态为CANCELED
                String oldStatus = record.getStatus();
                int updateResult = amBestDealRecordService.updateAmBestDealRecordStatus(id, "CANCELED");
                if (updateResult <= 0) {
                    errorMessages.add("更新状态失败，ID: " + id);
                    continue;
                }

                // 3. 创建取消RPA任务
                try {
                    String result = this.cancelBdActivity(record.getPromotionId(), operatorId);

                    log.info("成功创建取消RPA任务，记录ID: {}, 促销ID: {}, 结果: {}",
                            id, record.getPromotionId(), result);

                    successCount++;

                } catch (Exception e) {
                    log.error("创建取消RPA任务失败，记录ID: {}, 促销ID: {}", id, record.getPromotionId(), e);
                    // 取消RPA任务创建失败，需要回滚状态
                    amBestDealRecordService.updateAmBestDealRecordStatus(id, oldStatus);
                    errorMessages.add("创建取消RPA任务失败，ID: " + id + ", 错误: " + e.getMessage());
                }
            }

            // 如果有错误且没有成功的记录，抛出异常触发事务回滚
            if (successCount == 0 && !errorMessages.isEmpty()) {
                throw new BusinessException("批量取消失败: " + String.join("; ", errorMessages));
            }

            // 如果部分成功，记录警告日志但不回滚事务
            if (!errorMessages.isEmpty()) {
                log.warn("批量取消部分失败，成功: {}, 失败: {}, 错误信息: {}",
                        successCount, errorMessages.size(), String.join("; ", errorMessages));
            }

            log.info("批量取消BD促销记录完成，成功: {}, 失败: {}", successCount, errorMessages.size());
            return successCount;

        } catch (Exception e) {
            log.error("批量取消BD促销记录失败，操作人: {}", operatorId, e);
            throw new BusinessException("批量取消失败: " + e.getMessage());
        }
    }

    /**
     * 根据promotion_id构建亚马逊VC促销活动更新JSON
     * 更新活动使用
     *
     * @param promotionId 促销ID
     * @return 促销活动更新JSON字符串
     */
    public String buildUpdatePromotionJson(String promotionId) {
        if (StrUtil.isBlank(promotionId)) {
            throw new BusinessException("促销ID不能为空");
        }

        // 1. 查询促销记录
        AmBestDealRecord promotionRecord = amBestDealRecordService.selectAmBestDealRecordByPromotionId(promotionId);
        if (promotionRecord == null) {
            throw new BusinessException("未找到促销记录，促销ID: " + promotionId);
        }

        // 2. 查询关联的ASIN列表
        List<AmBestDealAsin> asinList = amBestDealAsinService.selectAmBestDealAsinByRefId(promotionRecord.getId());
        if (CollUtil.isEmpty(asinList)) {
            throw new BusinessException("未找到关联的ASIN记录，促销ID: " + promotionId);
        }

        // 3. 构建更新促销活动JSON
        Map<String, Object> result = buildUpdatePromotionData(promotionRecord, asinList);

        // 4. 转换为JSON字符串
        String jsonResult = JSONUtil.toJsonPrettyStr(result);

        log.info("成功构建更新促销活动JSON，促销ID: {}, ASIN数量: {}", promotionId, asinList.size());

        return jsonResult;
    }


    /**
     * 构建简化的促销活动数据（新格式）
     *
     * @param record   促销记录
     * @param asinList ASIN列表
     * @return 简化的促销活动数据
     */
    private Map<String, Object> buildSimplifiedPromotionData(AmBestDealRecord record, List<AmBestDealAsin> asinList) {
        Map<String, Object> result = new HashMap<>();

        // 构建产品列表
        List<Map<String, Object>> products = new ArrayList<>();
        for (AmBestDealAsin asin : asinList) {
            Map<String, Object> product = new HashMap<>();
            product.put("asin", asin.getPlatformGoodsId());
            product.put("sku", asin.getPlatformGoodsId());

            // 促销数量
            Map<String, Object> promotionQuantity = new HashMap<>();
            promotionQuantity.put("units", asin.getCommittedUnits() != null ? asin.getCommittedUnits() : 0);
            product.put("promotionQuantity", promotionQuantity);

            // 单位资金
            Map<String, Object> perUnitFunding = new HashMap<>();
            Map<String, Object> fundingValue = new HashMap<>();
            fundingValue.put("amount", asin.getPerUnitFunding() != null ? asin.getPerUnitFunding() : BigDecimal.ZERO);
            fundingValue.put("currencyCode", "USD");
            perUnitFunding.put("value", fundingValue);
            product.put("perUnitFunding", perUnitFunding);

            // 促销价格
            Map<String, Object> promotionPrice = new HashMap<>();
            Map<String, Object> priceValue = new HashMap<>();
            priceValue.put("amount", asin.getDealPrice() != null ? asin.getDealPrice() : BigDecimal.ZERO);
            priceValue.put("currencyCode", "USD");
            promotionPrice.put("value", priceValue);
            product.put("promotionPrice", promotionPrice);

            products.add(product);
        }
        result.put("products", products);

        // 构建促销信息
        Map<String, Object> promotion = new HashMap<>();
        promotion.put("internalDescription", buildInternalDescription(record));

        // 特色ASIN（使用第一个ASIN）
        if (!asinList.isEmpty()) {
            promotion.put("featuredAsin", asinList.get(0).getPlatformGoodsId());
        }

        promotion.put("marketplaceId", getMarketplaceId(record.getSite()));
        promotion.put("offeringName", "BEST_DEAL");

        // 时间安排
        Map<String, Object> schedule = new HashMap<>();
        schedule.put("startDate", convertToSimpleTimeFormat(record.getStartDateUtc()));
        schedule.put("endDate", convertToSimpleTimeFormat(record.getEndDateUtc()));
        promotion.put("schedule", schedule);

        // 所有者信息
        Map<String, Object> owner = new HashMap<>();
        owner.put("vendorCode", determineVendorCode(record.getPublishType(), record.getSite()));
        promotion.put("owner", owner);

        result.put("promotion", promotion);

        return result;
    }

    /**
     * 构建更新促销活动数据（更新格式）
     *
     * @param record   促销记录
     * @param asinList ASIN列表
     * @return 更新促销活动数据
     */
    private Map<String, Object> buildUpdatePromotionData(AmBestDealRecord record, List<AmBestDealAsin> asinList) {
        Map<String, Object> result = new HashMap<>();

        // 设置促销ID
        result.put("promotionId", record.getPromotionId());

        // 构建促销信息
        Map<String, Object> promotion = new HashMap<>();
        promotion.put("internalDescription", buildInternalDescription(record));

        // 特色ASIN（使用第一个ASIN）
        if (!asinList.isEmpty()) {
            promotion.put("featuredAsin", asinList.get(0).getPlatformGoodsId());
        }

        promotion.put("marketplaceId", getMarketplaceId(record.getSite()));
        promotion.put("offeringName", "BEST_DEAL");

        // 时间安排
        Map<String, Object> schedule = new HashMap<>();
        schedule.put("startDate", convertToSimpleTimeFormat(record.getStartDateUtc()));
        schedule.put("endDate", convertToSimpleTimeFormat(record.getEndDateUtc()));
        promotion.put("schedule", schedule);

        // 所有者信息
        Map<String, Object> owner = new HashMap<>();
        owner.put("vendorCode", determineVendorCode(record.getPublishType(), record.getSite()));
        promotion.put("owner", owner);

        result.put("promotion", promotion);

        // 构建更新的产品列表
        List<Map<String, Object>> updatedProducts = new ArrayList<>();
        for (AmBestDealAsin asin : asinList) {
            Map<String, Object> product = new HashMap<>();
            product.put("asin", asin.getPlatformGoodsId());
            product.put("sku", asin.getPlatformGoodsCode() != null ? asin.getPlatformGoodsCode() : asin.getPlatformGoodsId());

            // 促销数量
            Map<String, Object> promotionQuantity = new HashMap<>();
            promotionQuantity.put("units", asin.getCommittedUnits() != null ? asin.getCommittedUnits() : 0);
            product.put("promotionQuantity", promotionQuantity);

            // 单位资金
            Map<String, Object> perUnitFunding = new HashMap<>();
            Map<String, Object> fundingValue = new HashMap<>();
            fundingValue.put("amount", asin.getPerUnitFunding() != null ? asin.getPerUnitFunding() : BigDecimal.ZERO);
            fundingValue.put("currencyCode", "USD");
            perUnitFunding.put("value", fundingValue);
            product.put("perUnitFunding", perUnitFunding);

            // 促销价格
            Map<String, Object> promotionPrice = new HashMap<>();
            Map<String, Object> priceValue = new HashMap<>();
            priceValue.put("amount", asin.getDealPrice() != null ? asin.getDealPrice() : BigDecimal.ZERO);
            priceValue.put("currencyCode", "USD");
            promotionPrice.put("value", priceValue);
            product.put("promotionPrice", promotionPrice);

            updatedProducts.add(product);
        }

        result.put("updatedProducts", updatedProducts);

        // 移除的产品列表（暂时为空）
        result.put("removedProducts", new ArrayList<>());

        return result;
    }


    /**
     * 转换为简单时间格式 (ISO本地时间格式: yyyy-MM-ddTHH:mm:ss)
     */
    private String convertToSimpleTimeFormat(String utcTime) {
        if (StrUtil.isBlank(utcTime)) {
            return LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        }

        try {
            // 处理带时区信息的时间格式，如: "2025-08-19T23:45:00-07:00[US/Pacific]"
            if (utcTime.contains("[") || utcTime.contains("+") || utcTime.contains("-07:00") || utcTime.contains("-06:00")) {
                // 提取时间部分，去掉时区信息
                String timePart = utcTime;
                if (timePart.contains("[")) {
                    timePart = timePart.substring(0, timePart.indexOf("["));
                }
                if (timePart.contains("+")) {
                    timePart = timePart.substring(0, timePart.indexOf("+"));
                }
                if (timePart.contains("-") && timePart.lastIndexOf("-") > 10) {
                    timePart = timePart.substring(0, timePart.lastIndexOf("-"));
                }
                return timePart;
            }

            // 如果已经是ISO格式，去掉Z后缀
            if (utcTime.contains("T")) {
                return utcTime.replace("Z", "");
            }

            // 如果是其他格式，直接返回
            return utcTime;

        } catch (Exception e) {
            log.error("时间格式转换失败: {}", utcTime, e);
            return LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        }
    }


    /**
     * 构建内部描述
     */
    private String buildInternalDescription(AmBestDealRecord record) {
        String dateStr = "";
        if (StrUtil.isNotBlank(record.getStartDateUtc())) {
            try {
                // 解析日期并格式化为 "Aug 2, 2025" 格式
                String startDate = record.getStartDateUtc();
                // 如果包含时间部分，只取日期部分
                if (startDate.contains("T")) {
                    startDate = startDate.substring(0, startDate.indexOf("T"));
                }

                // 解析日期 (格式: yyyy-MM-dd)
                String[] dateParts = startDate.split("-");
                if (dateParts.length == 3) {
                    int year = Integer.parseInt(dateParts[0]);
                    int month = Integer.parseInt(dateParts[1]);
                    int day = Integer.parseInt(dateParts[2]);

                    // 月份名称数组
                    String[] monthNames = {"", "Jan", "Feb", "Mar", "Apr", "May", "Jun",
                            "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"};

                    dateStr = monthNames[month] + " " + day + ", " + year;
                } else {
                    dateStr = "Date";
                }
            } catch (Exception e) {
                dateStr = "Date";
            }
        }

        String vendorCode = determineVendorCode(record.getPublishType(), record.getSite());
        return dateStr + " " + vendorCode + " Best Deal";
    }

    /**
     * 获取市场ID
     */
    private String getMarketplaceId(String site) {
        if (site == null) return "ATVPDKIKX0DER";

        switch (site.toUpperCase()) {
            case "US":
                return "ATVPDKIKX0DER";
            case "UK":
                return "A1F83G8C2ARO7P";
            case "DE":
                return "A1PA6795UKMFR9";
            case "MX":
                return "A1AM78C64UM0Y8";
            default:
                return "ATVPDKIKX0DER";
        }
    }

    /**
     * 确定供应商代码
     */
    private String determineVendorCode(Integer publishType, String site) {
        if (publishType == null) return "WM741";

        // 根据刊登类型和站点确定供应商代码
        if (publishType == 5) { // VCDF
            return "WM741";
        } else if (publishType == 6) { // VCPO
            return "IH75B";
        }

        return "WM741"; // 默认值
    }

    /**
     * 比较新旧ASIN数据，找出新增、修改、删除的ASIN
     *
     * @param newRecord 新的BD记录
     * @param oldRecord 旧的BD记录
     * @return ASIN比较结果
     */
    private AsinComparisonResult compareAsinData(AmBestDealRecord newRecord, AmBestDealRecord oldRecord) {
        AsinComparisonResult result = new AsinComparisonResult();

        // 获取新旧ASIN列表
        List<AmBestDealAsin> newAsins = newRecord.getAsinList() != null ? newRecord.getAsinList() : new ArrayList<>();
        List<AmBestDealAsin> oldAsins = oldRecord.getAsinList() != null ? oldRecord.getAsinList() : new ArrayList<>();

        // 创建ASIN映射表，便于比较
        Map<String, AmBestDealAsin> oldAsinMap = oldAsins.stream()
                .collect(HashMap::new, (map, asin) -> map.put(asin.getPlatformGoodsId(), asin), HashMap::putAll);
        Map<String, AmBestDealAsin> newAsinMap = newAsins.stream()
                .collect(HashMap::new, (map, asin) -> map.put(asin.getPlatformGoodsId(), asin), HashMap::putAll);

        // 找出新增的ASIN
        for (AmBestDealAsin newAsin : newAsins) {
            if (!oldAsinMap.containsKey(newAsin.getPlatformGoodsId())) {
                result.addAddedAsin(newAsin);
            }
        }

        // 找出删除的ASIN
        for (AmBestDealAsin oldAsin : oldAsins) {
            if (!newAsinMap.containsKey(oldAsin.getPlatformGoodsId())) {
                result.addRemovedAsin(oldAsin);
            }
        }

        // 找出修改的ASIN
        for (AmBestDealAsin newAsin : newAsins) {
            AmBestDealAsin oldAsin = oldAsinMap.get(newAsin.getPlatformGoodsId());
            if (oldAsin != null && isAsinDataChanged(newAsin, oldAsin)) {
                result.addModifiedAsin(newAsin, oldAsin);
            }
        }

        return result;
    }

    /**
     * 判断ASIN数据是否发生变化
     *
     * @param newAsin 新ASIN数据
     * @param oldAsin 旧ASIN数据
     * @return 是否发生变化
     */
    private boolean isAsinDataChanged(AmBestDealAsin newAsin, AmBestDealAsin oldAsin) {
        // 比较关键字段
        return !Objects.equals(newAsin.getCommittedUnits(), oldAsin.getCommittedUnits()) ||
                !Objects.equals(newAsin.getDealPrice(), oldAsin.getDealPrice()) ||
                !Objects.equals(newAsin.getPerUnitFunding(), oldAsin.getPerUnitFunding()) ||
                !Objects.equals(newAsin.getStandardPrice(), oldAsin.getStandardPrice()) ||
                !Objects.equals(newAsin.getReferencePrice(), oldAsin.getReferencePrice()) ||
                !Objects.equals(newAsin.getActualDiscount(), oldAsin.getActualDiscount()) ||
                !Objects.equals(newAsin.getExpectedDemand(), oldAsin.getExpectedDemand());
    }

    /**
     * ASIN比较结果内部类
     */
    private static class AsinComparisonResult {
        private final List<AmBestDealAsin> addedAsins = new ArrayList<>();
        private final List<AmBestDealAsin> removedAsins = new ArrayList<>();
        private final List<AsinModification> modifiedAsins = new ArrayList<>();

        public void addAddedAsin(AmBestDealAsin asin) {
            addedAsins.add(asin);
        }

        public void addRemovedAsin(AmBestDealAsin asin) {
            removedAsins.add(asin);
        }

        public void addModifiedAsin(AmBestDealAsin newAsin, AmBestDealAsin oldAsin) {
            modifiedAsins.add(new AsinModification(newAsin, oldAsin));
        }

        public List<AmBestDealAsin> getAddedAsins() {
            return addedAsins;
        }

        public List<AmBestDealAsin> getRemovedAsins() {
            return removedAsins;
        }

        public List<AsinModification> getModifiedAsins() {
            return modifiedAsins;
        }

        public boolean hasChanges() {
            return !addedAsins.isEmpty() || !removedAsins.isEmpty() || !modifiedAsins.isEmpty();
        }
    }

    /**
     * ASIN修改记录内部类
     */
    private static class AsinModification {
        private final AmBestDealAsin newAsin;
        private final AmBestDealAsin oldAsin;

        public AsinModification(AmBestDealAsin newAsin, AmBestDealAsin oldAsin) {
            this.newAsin = newAsin;
            this.oldAsin = oldAsin;
        }

        public AmBestDealAsin getNewAsin() {
            return newAsin;
        }

        public AmBestDealAsin getOldAsin() {
            return oldAsin;
        }
    }

    /**
     * 比较新旧BD活动数据，找出新增、修改、删除的ASIN以及活动信息变更
     *
     * @param newActivityData 新的BD活动数据
     * @param oldActivityData 旧的BD活动数据
     * @return BD活动比较结果
     */
    private BdActivityComparisonResult compareBdActivityData(BdActivityDTO newActivityData, BdActivityDTO oldActivityData) {
        BdActivityComparisonResult result = new BdActivityComparisonResult();

        // 比较活动基本信息是否变更
        boolean activityInfoChanged = isActivityInfoChanged(newActivityData.getActivityRecord(),
                oldActivityData != null ? oldActivityData.getActivityRecord() : null);
        result.setActivityInfoChanged(activityInfoChanged);

        // 获取新旧ASIN列表
        List<AmBestDealAsin> newAsins = newActivityData.getAsinList() != null ?
                newActivityData.getAsinList() : new ArrayList<>();
        List<AmBestDealAsin> oldAsins = oldActivityData != null && oldActivityData.getAsinList() != null ?
                oldActivityData.getAsinList() : new ArrayList<>();

        // 创建ASIN映射表，便于比较
        Map<String, AmBestDealAsin> oldAsinMap = oldAsins.stream()
                .collect(HashMap::new, (map, asin) -> map.put(asin.getPlatformGoodsId(), asin), HashMap::putAll);
        Map<String, AmBestDealAsin> newAsinMap = newAsins.stream()
                .collect(HashMap::new, (map, asin) -> map.put(asin.getPlatformGoodsId(), asin), HashMap::putAll);

        // 找出新增的ASIN
        for (AmBestDealAsin newAsin : newAsins) {
            if (!oldAsinMap.containsKey(newAsin.getPlatformGoodsId())) {
                result.addAddedAsin(newAsin);
            }
        }

        // 找出删除的ASIN
        for (AmBestDealAsin oldAsin : oldAsins) {
            if (!newAsinMap.containsKey(oldAsin.getPlatformGoodsId())) {
                result.addRemovedAsin(oldAsin);
            }
        }

        // 找出修改的ASIN
        for (AmBestDealAsin newAsin : newAsins) {
            AmBestDealAsin oldAsin = oldAsinMap.get(newAsin.getPlatformGoodsId());
            if (oldAsin != null && isBdAsinDataChanged(newAsin, oldAsin)) {
                result.addModifiedAsin(newAsin, oldAsin);
            }
        }

        return result;
    }

    /**
     * 判断BD活动基本信息是否发生变化
     *
     * @param newRecord 新活动记录
     * @param oldRecord 旧活动记录
     * @return 是否发生变化
     */
    private boolean isActivityInfoChanged(AmBestDealRecord newRecord, AmBestDealRecord oldRecord) {
        if (oldRecord == null) {
            return true; // 新增活动
        }

        return !Objects.equals(newRecord.getPromotionName(), oldRecord.getPromotionName()) ||
                !Objects.equals(newRecord.getStartDateUtc(), oldRecord.getStartDateUtc()) ||
                !Objects.equals(newRecord.getEndDateUtc(), oldRecord.getEndDateUtc()) ||
                !Objects.equals(newRecord.getStatus(), oldRecord.getStatus()) ||
                !Objects.equals(newRecord.getFundingAgreementId(), oldRecord.getFundingAgreementId()) ||
                !Objects.equals(newRecord.getEventType(), oldRecord.getEventType());
    }

    /**
     * 判断BD ASIN数据是否发生变化
     *
     * @param newAsin 新ASIN数据
     * @param oldAsin 旧ASIN数据
     * @return 是否发生变化
     */
    private boolean isBdAsinDataChanged(AmBestDealAsin newAsin, AmBestDealAsin oldAsin) {
        // 比较关键字段
        return !Objects.equals(newAsin.getCommittedUnits(), oldAsin.getCommittedUnits()) ||
                !Objects.equals(newAsin.getDealPrice(), oldAsin.getDealPrice()) ||
                !Objects.equals(newAsin.getPerUnitFunding(), oldAsin.getPerUnitFunding()) ||
                !Objects.equals(newAsin.getStandardPrice(), oldAsin.getStandardPrice()) ||
                !Objects.equals(newAsin.getReferencePrice(), oldAsin.getReferencePrice()) ||
                !Objects.equals(newAsin.getActualDiscount(), oldAsin.getActualDiscount()) ||
                !Objects.equals(newAsin.getExpectedDemand(), oldAsin.getExpectedDemand()) ||
                !Objects.equals(newAsin.getLowestDiscount(), oldAsin.getLowestDiscount());
    }

    /**
     * BD活动比较结果内部类
     */
    private static class BdActivityComparisonResult {
        private final List<AmBestDealAsin> addedAsins = new ArrayList<>();
        private final List<AmBestDealAsin> removedAsins = new ArrayList<>();
        private final List<BdAsinModification> modifiedAsins = new ArrayList<>();
        private boolean activityInfoChanged = false;

        public void addAddedAsin(AmBestDealAsin asin) {
            addedAsins.add(asin);
        }

        public void addRemovedAsin(AmBestDealAsin asin) {
            removedAsins.add(asin);
        }

        public void addModifiedAsin(AmBestDealAsin newAsin, AmBestDealAsin oldAsin) {
            modifiedAsins.add(new BdAsinModification(newAsin, oldAsin));
        }

        public List<AmBestDealAsin> getAddedAsins() {
            return addedAsins;
        }

        public List<AmBestDealAsin> getRemovedAsins() {
            return removedAsins;
        }

        public List<BdAsinModification> getModifiedAsins() {
            return modifiedAsins;
        }

        public boolean isActivityInfoChanged() {
            return activityInfoChanged;
        }

        public void setActivityInfoChanged(boolean activityInfoChanged) {
            this.activityInfoChanged = activityInfoChanged;
        }

        public boolean hasChanges() {
            return !addedAsins.isEmpty() || !removedAsins.isEmpty() || !modifiedAsins.isEmpty() || activityInfoChanged;
        }
    }

    /**
     * BD ASIN修改记录内部类
     */
    private static class BdAsinModification {
        private final AmBestDealAsin newAsin;
        private final AmBestDealAsin oldAsin;

        public BdAsinModification(AmBestDealAsin newAsin, AmBestDealAsin oldAsin) {
            this.newAsin = newAsin;
            this.oldAsin = oldAsin;
        }

        public AmBestDealAsin getNewAsin() {
            return newAsin;
        }

        public AmBestDealAsin getOldAsin() {
            return oldAsin;
        }
    }

}
