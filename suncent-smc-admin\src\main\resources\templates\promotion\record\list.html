<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('BD促销记录列表')" />
    <style>
        /* 折扣汇总信息样式 */
        .discount-summary {
            font-size: 12px;
            line-height: 1.4;
            min-width: 180px;
        }
        .discount-summary div {
            margin-bottom: 2px;
        }
        .discount-summary div:last-child {
            margin-bottom: 0;
        }

        /* 活动日期样式 */
        .activity-date {
            white-space: nowrap;
        }

        /* 表格列宽度调整 */
        .table th:nth-child(8) { /* 活动日期列 */
            min-width: 160px;
        }
        .table th:nth-child(9) { /* 折扣列 */
            min-width: 200px;
        }
    </style>
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>促销名称：</label>
                                <input type="text" name="promotionName" placeholder="请输入促销名称"/>
                            </li>
                            <li>
                                <label>状态：</label>
                                <select name="status" id="statusSelect">
                                    <option value="">所有</option>
                                    <!-- 动态加载状态选项 -->
                                </select>
                            </li>
                            <li>
                                <label>Event：</label>
                                <select name="eventType">
                                    <option value="">所有</option>
                                    <option value="1">自定义日期</option>
                                    <option value="2">会员日</option>
                                    <option value="3">黑五</option>
                                </select>
                            </li>
                            <li>
                                <label>刊登类型：</label>
                                <select name="publishType">
                                    <option value="">所有</option>
                                    <option value="5">VCDF</option>
                                    <option value="6">VCPO</option>
                                </select>
                            </li>
                            <li>
                                <label>关键字：</label>
                                <input type="text" name="keyword" placeholder="促销名称或促销ID"/>
                            </li>
                            <li style="height: 30px">
                                <label>时间：</label>
                                <select data-none-selected-text="" title="请选择时间" class="child-class noselect2 selectpicker" name="timeType" id="timeType">
                                    <option value="">请选择时间</option>
                                    <option value="1">创建时间</option>
                                    <option value="2">活动日期</option>
                                </select>
                                <input style="width: 212px;" type="text" class="time-input" id="startTime" placeholder="开始时间" name="params[beginTime]"/>
                                <span style="padding: 0px 8px;">-</span>
                                <input style="width: 212px;" type="text" class="time-input" id="endTime" placeholder="结束时间" name="params[endTime]"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="promotion:record:add">
                    <i class="fa fa-plus"></i> 批量新建
                </a>
                <a class="btn btn-success" onclick="$.operate.confirm()" shiro:hasPermission="promotion:record:confirm">
                    <i class="fa fa-check"></i> 批量确认
                </a>
                <a class="btn btn-warning" onclick="$.operate.cancel()" shiro:hasPermission="promotion:record:cancel">
                    <i class="fa fa-times"></i> 批量取消
                </a>
<!--                <a class="btn btn-warning" onclick="$.operate.import()" shiro:hasPermission="promotion:record:import">-->
<!--                    <i class="fa fa-upload"></i> 批量导入-->
<!--                </a>-->
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="promotion:record:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('promotion:record:edit')}]];
        var removeFlag =[[${@permission.hasPermi('promotion:record:remove')}]];
        var logFlag =[[${@permission.hasPermi('promotion:record:view')}]];
        var prefix = ctx + "promotion/record";

        $(function() {
            console.log('Document ready, initializing table...');
            console.log('jQuery version:', $.fn.jquery);
            console.log('Bootstrap table available:', typeof $.fn.bootstrapTable);
            console.log('Table element:', $('#bootstrap-table'));

            // 初始化状态下拉框
            loadStatusOptions();

            // 初始化时间选择器
            initTimeSelectors();

            // 初始化laydate日期选择器
            initLayDatePickers();

            var options = {
                url: prefix + "/list",  // 暂时注释掉API调用
                // data: testData,  // 使用静态测试数据
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "BD促销记录",
                sortName: "createTime",
                sortOrder: "desc",
                pageSize: 25,
                columns: [{
                    checkbox: true
                }, {
                    field: 'id',
                    title: 'ID',
                    visible: false
                }, {
                    field: 'promotionId',
                    title: '促销ID',
                }, {
                    field: 'promotionName',
                    title: '促销名称',
                }, {
                    field: 'status',
                    title: '状态',
                    formatter: function(value, row, index) {
                        return '<span class="label label-default">' + value + '</span>';
                    }
                }, {
                    field: 'activityDate',
                    title: '活动日期',
                    width: 10,
                    widthUnit: '%',
                    formatter: function(value, row, index) {
                        return formatActivityDate(row);
                    }
                }, {
                    field: 'discountSummary',
                    title: '折扣',
                    formatter: function(value, row, index) {
                        return formatDiscountSummary(row);
                    }
                },
                 {
                    field: 'eventType',
                    title: 'Event',
                    formatter: function(value, row, index) {
                        if (value == 1) return '<span class="label label-primary">自定义日期</span>';
                        if (value == 2) return '<span class="label label-info">会员日</span>';
                        if (value == 3) return '<span class="label label-success">黑五</span>';
                        return '<span class="label label-default">未知</span>';
                    }
                },  {
                    field: 'publishType',
                    title: '刊登类型',
                    formatter: function(value, row, index) {
                        if (value == 5) return '<span class="label label-primary">VCDF</span>';
                        if (value == 6) return '<span class="label label-info">VCPO</span>';
                        return '<span class="label label-default">未知</span>';
                    }
                }, {
                    field: 'asinCount',
                    title: 'ASIN数量',
                    formatter: function(value, row, index) {
                        return '<span class="badge badge-primary">' + (value || 0) + '</span>';
                    }
                }, {
                    field: 'createTime',
                    title: '创建时间',
                    width: 10,
                    widthUnit: '%',
                }, {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        // 详情按钮 - 查看权限
                        actions.push('<a class="btn btn-primary btn-xs" href="javascript:void(0)" shiro:hasPermission="promotion:record:view" onclick="viewRecord(\'' + row.id + '\')"><i class="fa fa-eye"></i>详情</a> ');
                        // 编辑按钮 - 使用弹窗模式
                        actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)"  shiro:hasPermission="promotion:record:edit" onclick="editRecord(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        // 日志按钮 - 需要查看权限
                        actions.push('<a class="btn btn-info btn-xs" href="javascript:void(0)" shiro:hasPermission="promotion:record:view" onclick="showLogs(\'' + row.id + '\')"><i class="fa fa-list"></i>日志</a> ');
                        // 删除按钮 - 需要删除权限
                        // actions.push('<a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join(' ');
                    }
                }]
            };

            $.table.init(options);
        });

        // 显示操作日志
        function showLogs(id) {
            $.modal.openNoYes('BD促销日志', prefix + "/logsIntegrated/" + id, 1200,700);
        }

        // 自定义新增方法 - 打开批量工作区
        $.operate.add = function() {
            $.modal.openTab("新增BD促销", prefix + "/add" );
        }

        // 自定义批量确认方法
        $.operate.confirm = function() {
            var rows = $.table.selectFirstColumns();
            if (rows.length == 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }

            // 检查选中记录数量限制
            if (rows.length > 50) {
                $.modal.alertWarning("单次批量确认不能超过50条记录");
                return;
            }

            $.modal.confirm("确认要批量确认选中的BD促销记录吗？<br><span style='color: red;'>注意：只有草稿状态的记录才能被确认</span>", function() {
                // 显示加载状态
                var confirmBtn = $('button[onclick="$.operate.confirm()"]');
                var originalText = confirmBtn.html();
                confirmBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 确认中...');

                $.operate.post(prefix + "/batchConfirm", {ids: rows.join()}, function(result) {
                    // 恢复按钮状态
                    confirmBtn.prop('disabled', false).html(originalText);

                    if (result.code == web_status.SUCCESS) {
                        $.modal.msgSuccess(result.msg);
                        // 刷新表格
                        $("#bootstrap-table").bootstrapTable('refresh');
                    } else {
                        $.modal.alertError(result.msg);
                    }
                });
            });
        }

        // 自定义批量取消方法
        $.operate.cancel = function() {
            var rows = $.table.selectFirstColumns();
            if (rows.length == 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }

            // 检查选中记录数量限制
            if (rows.length > 50) {
                $.modal.alertWarning("单次批量取消不能超过50条记录");
                return;
            }

            $.modal.confirm("确认要批量取消选中的BD促销记录吗？<br><span style='color: red;'>注意：只有更新中状态的记录才能被取消</span>", function() {
                // 显示加载状态
                var cancelBtn = $('button[onclick="$.operate.cancel()"]');
                var originalText = cancelBtn.html();
                cancelBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 取消中...');

                $.operate.post(prefix + "/batchCancel", {ids: rows.join()}, function(result) {
                    // 恢复按钮状态
                    cancelBtn.prop('disabled', false).html(originalText);

                    if (result.code == web_status.SUCCESS) {
                        $.modal.msgSuccess(result.msg);
                        // 刷新表格
                        $("#bootstrap-table").bootstrapTable('refresh');
                    } else {
                        $.modal.alertError(result.msg);
                    }
                });
            });
        }

        // 查看记录详情 - 使用弹窗模式
        function viewRecord(recordId) {
            $.modal.openNoYes('查看BD促销详情', prefix + "/viewModal?id=" + recordId, 1200,700);
        }

        // 编辑记录 - 使用弹窗模式
        function editRecord(recordId) {
            // 直接打开编辑弹窗，通过recordId参数传递
            var options = {
                title: '编辑BD促销',
                url: prefix + "/editModal?id=" + recordId,
                width: '1200',
                height: '700',
                callBack: function(index, layero) {
                    try {
                        // 获取子页面的window对象
                        var iframe = layero.find('iframe')[0];
                        var childWindow = iframe.contentWindow;

                        // 调用子页面的submitHandler函数进行验证和数据准备
                        if (childWindow && typeof childWindow.submitHandler === 'function') {
                            var submitResult = childWindow.submitHandler(index, layero);

                            if (submitResult && childWindow.dealData) {
                                // 验证通过且有数据，提交到后端
                                var updatedData = childWindow.dealData;
                                updatedData.id = recordId; // 确保ID正确

                                // 调用后端更新接口
                                $.ajax({
                                    url: prefix + "/edit",
                                    type: "POST",
                                    contentType: "application/json",
                                    dataType: "json",
                                    data: JSON.stringify(updatedData),
                                    success: function(result) {
                                        if (result.code === 0) {
                                            $.modal.msgSuccess("修改成功");
                                            $.modal.close(index);
                                            // 刷新表格
                                            $("#bootstrap-table").bootstrapTable('refresh');
                                        } else {
                                            $.modal.alertError("修改失败：" + (result.msg || "未知错误"));
                                        }
                                    },
                                    error: function(xhr, status, error) {
                                        $.modal.alertError('修改失败：' + error);
                                    }
                                });
                            } else {
                                // 验证失败，不关闭窗口
                                return false;
                            }
                        } else {
                            console.error('无法找到子页面的submitHandler函数');
                            return false;
                        }
                    } catch (e) {
                        console.error('调用子页面函数失败:', e);
                        return false;
                    }
                }
            };
            $.modal.openOptions(options);
        }

        // 截断日期时间字符串，只保留日期部分
        function extractDateOnly(dateTimeString) {
            if (!dateTimeString) return '';

            // 处理格式：2025-03-19T00:00:00-07:00[US/Pacific]
            // 提取 YYYY-MM-DD 部分
            var match = dateTimeString.match(/^(\d{4}-\d{2}-\d{2})/);
            return match ? match[1] : dateTimeString;
        }

        // 格式化活动日期
        function formatActivityDate(row) {
            var eventType = row.eventType;
            var startDate = row.startDateUtc;
            var endDate = row.endDateUtc;

            // 根据事件类型显示不同内容
            if (eventType == 2) {
                // 会员日
                return '<span class="text-info"><i class="fa fa-star"></i> Prime Big Deal Days</span>';
            } else if (eventType == 3) {
                // 黑五
                return '<span class="text-success"><i class="fa fa-shopping-cart"></i> Black Friday/Cyber Monday</span>';
            } else {
                // 自定义日期 - 截断时间部分，只显示日期
                if (startDate && endDate) {
                    var startDateOnly = extractDateOnly(startDate);
                    var endDateOnly = extractDateOnly(endDate);
                    return '<span class="text-primary">' + startDateOnly + ' - ' + endDateOnly + '</span>';
                } else if (startDate) {
                    var startDateOnly = extractDateOnly(startDate);
                    return '<span class="text-primary">' + startDateOnly + '</span>';
                } else {
                    return '<span class="text-muted">未设置</span>';
                }
            }
        }

        // 格式化折扣汇总信息
        function formatDiscountSummary(row) {
            // 从ASIN列表中计算汇总数据
            var asinList = row.asinList || [];

            if (asinList.length === 0) {
                return '<span class="text-muted">暂无数据</span>';
            }

            // 计算汇总数据
            var totalCommittedUnits = 0;
            var totalFunding = 0;

            for (var i = 0; i < asinList.length; i++) {
                var asin = asinList[i];

                // 累计承诺数量
                if (asin.committedUnits) {
                    totalCommittedUnits += parseInt(asin.committedUnits) || 0;
                }

                // 累计funding
                if (asin.perUnitFunding) {
                    totalFunding += (parseFloat(asin.perUnitFunding) * parseInt(asin.committedUnits)) || 0;
                }
            }

            var discount = asinList?  asinList[0].actualDiscount : 0;

            // 格式化显示
            var html = '<div class="discount-summary">';
            html += '<div><strong>Discount:</strong> <span class="text-success">' + discount + '%</span></div>';
            html += '<div><strong>Committed units:</strong> <span class="text-primary">' + totalCommittedUnits.toLocaleString() + '</span></div>';
            html += '<div><strong>Funding:</strong> <span class="text-warning">$' + totalFunding.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2}) + '</span></div>';
            html += '</div>';

            return html;
        }

        // 加载状态选项
        function loadStatusOptions() {
            $.ajax({
                url: prefix + "/getStatusList",
                type: "POST",
                dataType: "json",
                success: function(response) {
                    if (response.code === 0 && response.data) {
                        var statusSelect = $("#statusSelect");
                        statusSelect.empty();
                        statusSelect.append('<option value="">所有</option>');

                        for (var i = 0; i < response.data.length; i++) {
                            var status = response.data[i];
                            statusSelect.append('<option value="' + status + '">' + status + '</option>');
                        }
                    }
                },
                error: function(xhr, status, error) {
                    console.error('加载状态选项失败:', error);
                }
            });
        }

        // 初始化laydate日期选择器
        function initLayDatePickers() {
            layui.use('laydate', function() {
                var laydate = layui.laydate;

                // 开始时间选择器
                laydate.render({
                    elem: '#startTime',
                    type: 'date',
                    format: 'yyyy-MM-dd',
                    theme: 'molv'
                });

                // 结束时间选择器
                laydate.render({
                    elem: '#endTime',
                    type: 'date',
                    format: 'yyyy-MM-dd',
                    theme: 'molv'
                });
            });
        }

        // 初始化时间选择器
        function initTimeSelectors() {
            // 监听时间类型变化
            $('#timeType').on('change', function() {
                var timeType = $(this).val();
                var startTimeInput = $('#startTime');
                var endTimeInput = $('#endTime');

                // 清空时间输入框
                startTimeInput.val('');
                endTimeInput.val('');

                // 根据时间类型设置参数名
                if (timeType === '1') {
                    // 创建时间
                    startTimeInput.attr('name', 'params[beginTime]');
                    endTimeInput.attr('name', 'params[endTime]');
                } else if (timeType === '2') {
                    // 活动日期
                    startTimeInput.attr('name', 'params[startDateFrom]');
                    endTimeInput.attr('name', 'params[startDateTo]');
                } else {
                    // 未选择时间类型，清空name属性
                    startTimeInput.removeAttr('name');
                    endTimeInput.removeAttr('name');
                }
            });

            // 初始化时清空name属性
            $('#startTime').removeAttr('name');
            $('#endTime').removeAttr('name');
        }
    </script>
</body>
</html>
