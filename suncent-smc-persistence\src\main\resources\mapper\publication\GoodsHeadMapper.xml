<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.suncent.smc.persistence.publication.mapper.GoodsHeadMapper">

    <resultMap type="GoodsHead" id="ListingGoodsHeadResult">
        <result property="id" column="id"/>
        <result property="pdmGoodsCode" column="pdm_goods_code"/>
        <result property="platformGoodsCode" column="platform_goods_code"/>
        <result property="platformGoodsId" column="platform_goods_id"/>
        <result property="platform" column="platform"/>
        <result property="shopCode" column="shop_code"/>
        <result property="publishType" column="publish_type"/>
        <result property="condition" column="condition"/>
        <result property="title" column="title"/>
        <result property="subtitle" column="subtitle"/>
        <result property="publishStatus" column="publish_status"/>
        <result property="mainImageUrl" column="main_image_url"/>
        <result property="siteCode" column="site_code"/>
        <result property="brandCode" column="brand_code"/>
        <result property="standardPrice" column="standard_price"/>
        <result property="originalPrice" column="original_price"/>
        <result property="settlementPrice" column="settlement_price"/>
        <result property="redLinePrice" column="red_line_price"/>
        <result property="stockOnSalesQty" column="stock_on_sales_qty"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="del_flag"/>
        <result property="categoryId" column="category_id"/>
        <result property="publishingHandler" column="publishing_handler"/>
        <result property="taskName" column="task_name"/>
        <result property="smcFlag" column="smc_flag"/>
        <result property="censorship" column="censorship"/>
        <result property="pdmStatus" column="pdm_status"/>
        <result property="onlineTime" column="online_time"/>
        <result property="listingRateLabel" column="listing_rate_label"/>
        <result property="skuRateLabel" column="sku_rate_label"/>
        <result property="isMain" column="is_main"/>
        <result property="productSeries" column="product_series"/>
        <result property="productModel" column="product_model"/>
    </resultMap>

    <resultMap type="GoodsHeadVO" id="ListingGoodsHeadVOResult">
        <result property="id" column="id"/>
        <result property="pdmGoodsCode" column="pdm_goods_code"/>
        <result property="platformGoodsCode" column="platform_goods_code"/>
        <result property="platformGoodsId" column="platform_goods_id"/>
        <result property="platform" column="platform"/>
        <result property="shopCode" column="shop_code"/>
        <result property="publishType" column="publish_type"/>
        <result property="condition" column="condition"/>
        <result property="title" column="title"/>
        <result property="subtitle" column="subtitle"/>
        <result property="adaptationStatus" column="adaptation_status"/>
        <result property="publishStatus" column="publish_status"/>
        <result property="mainImageUrl" column="main_image_url"/>
        <result property="siteCode" column="site_code"/>
        <result property="brandCode" column="brand_code"/>
        <result property="standardPrice" column="standard_price"/>
        <result property="originalPrice" column="original_price"/>
        <result property="settlementPrice" column="settlement_price"/>
        <result property="redLinePrice" column="red_line_price"/>
        <result property="stockOnSalesQty" column="stock_on_sales_qty"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="del_flag"/>
        <result property="categoryId" column="category_id"/>
        <result property="onlineTime" column="online_time"/>
        <result property="offTime" column="off_time"/>
        <result property="taskName" column="task_name"/>
        <result property="smcFlag" column="smc_flag"/>
        <result property="pdmStatus" column="pdm_status"/>
        <result property="censorship" column="censorship"/>
        <result property="listingRateLabel" column="listing_rate_label"/>
        <result property="skuRateLabel" column="sku_rate_label"/>
        <result property="isMain" column="is_main"/>
        <result property="productSeries" column="product_series"/>
        <result property="productModel" column="product_model"/>

    </resultMap>

    <resultMap type="ListingPublishStatusCountDTO" id="ListingPublishStatusCountDTOResult">
        <result property="allCount" column="allCount"/>
        <result property="draftCount" column="draftCount"/>
        <result property="publishCount" column="publishCount"/>
        <result property="saleCount" column="saleCount"/>
        <result property="updatingCount" column="updatingCount"/>
        <result property="updateFailedCount" column="updateFailedCount"/>
        <result property="offShelfCount" column="offShelfCount"/>
        <result property="noSaleCount" column="noSaleCount"/>
        <result property="offShelfFailedCount" column="offShelfFailedCount"/>
        <result property="publishFailedCount" column="publishFailedCount"/>
    </resultMap>
    <resultMap type="com.suncent.smc.persistence.publication.domain.vo.GoodsHeadHomePageVO" id="homePageList">
        <result property="platform" column="platform"/>
        <result property="groundingListing" column="groundingListing"/>
        <result property="salesingListing" column="salesingListing"/>
        <result property="offEB" column="offEB"/>
        <result property="offAM" column="offAM"/>
        <result property="onlineTime" column="online_time"/>
    </resultMap>

    <sql id="selectListingGoodsHeadVo">
        select id,
               pdm_goods_code,
               platform_goods_code,
               platform_goods_id,
               platform,
               shop_code,
               publish_type,
               `condition`,
               title,
               subtitle,
               adaptation_status,
               publish_status,
               main_image_url,
               site_code,
               brand_code,
               standard_price,
               original_price,
               settlement_price,
               red_line_price,
               stock_on_sales_qty,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               del_flag,
               category_id,
               publishing_handler,
               online_time,
               off_time,
               task_name,
               smc_flag,
               censorship,
               pdm_status,
               listing_rate_label,
               sku_rate_label,
               is_main,product_series,product_model
        from sc_smc_listing_goods_head
    </sql>


    <sql id="selectListingGoodsHeadVo2">
        select id,
               pdm_goods_code,
               platform_goods_code,
               platform_goods_id,
               platform,
               shop_code,
               publish_type,
               `condition`,
               title,
               subtitle,
               adaptation_status,
               publish_status,
               main_image_url,
               site_code,
               brand_code,
               standard_price,
               original_price,
               settlement_price,
               red_line_price,
               stock_on_sales_qty,
               create_by,
               create_time,
               update_by,
               update_time,
               del_flag,
               category_id,
               publishing_handler,
               online_time,
               off_time,
               task_name,
               smc_flag,
               censorship,
               pdm_status,
               listing_rate_label,
               sku_rate_label,
               is_main,product_series,product_model
        from sc_smc_listing_goods_head
    </sql>
    <sql id="selectListingGoodsHeadVoCount">
        select publish_status,COUNT(*) as counts
        from sc_smc_listing_goods_head
    </sql>

    <select id="selectListingGoodsHeadList" parameterType="GoodsHead" resultMap="ListingGoodsHeadResult">
        <include refid="selectListingGoodsHeadVo"/>
        WHERE del_flag = 0
        <if test="pdmGoodsCode != null  and pdmGoodsCode != ''">and pdm_goods_code = #{pdmGoodsCode}</if>
        <if test="platformGoodsCode != null  and platformGoodsCode != ''">and platform_goods_code =
            #{platformGoodsCode}
        </if>
        <if test="platformGoodsId != null  and platformGoodsId != ''">and platform_goods_id = #{platformGoodsId}
        </if>
        <if test="productSeries != null  and productSeries != ''">and product_series = #{productSeries}</if>
        <if test="productModel != null  and productModel != ''">and product_model = #{productModel}</if>
        <if test="platform != null  and platform != ''">and platform = #{platform}</if>
        <if test="shopCode != null  and shopCode != ''">and shop_code = #{shopCode}</if>
        <if test="publishType != null  and publishType != ''">and publish_type = #{publishType}</if>
        <if test="condition != null  and condition != ''">and `condition` = #{condition}</if>
        <if test="title != null  and title != ''">and title like concat('%', #{title}, '%')</if>
        <if test="subtitle != null  and subtitle != ''">and subtitle like concat('%', #{subtitle}, '%')</if>
        <if test="publishStatus != null ">and publish_status = '${publishStatus}'</if>
        <if test="mainImageUrl != null  and mainImageUrl != ''">and main_image_url = #{mainImageUrl}</if>
        <if test="siteCode != null  and siteCode != ''">and site_code = #{siteCode}</if>
        <if test="brandCode != null  and brandCode != ''">and brand_code = #{brandCode}</if>
        <if test="standardPrice != null ">and standard_price = #{standardPrice}</if>
        <if test="originalPrice != null ">and original_price = #{originalPrice}</if>
        <if test="settlementPrice != null ">and settlement_price = #{settlementPrice}</if>
        <if test="redLinePrice != null ">and red_line_price = #{redLinePrice}</if>
        <if test="stockOnSalesQty != null ">and stock_on_sales_qty = #{stockOnSalesQty}</if>
        <if test="delFlag != null ">and del_flag = #{delFlag}</if>
        <if test="categoryId != null ">and category_id = #{categoryId}</if>
        <if test="publishingHandler != null ">and publishing_handler = #{publishingHandler}</if>
        <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
        <if test="categoryIds != null and  categoryIds != ''">and category_id in
            <foreach item="code" collection="categoryIds.split(',')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="publishStatusList != null and publishStatusList.size() != 0">
        and publish_status in
         <foreach collection="publishStatusList" open="(" separator="," close=")" item="publishStatus">
             '${publishStatus}'
        </foreach>
        </if>
        <if test="publishTypeList != null and publishTypeList.size() != 0">
            and publish_type in
            <foreach collection="publishTypeList" open="(" separator="," close=")" item="publishType">
                #{publishType}
            </foreach>
        </if>
        <if test="platformGoodsCodeList != null and platformGoodsCodeList.size() != 0">
            and platform_goods_code in
            <foreach collection="platformGoodsCodeList" open="(" separator="," close=")" item="platformGoodsCode">
                #{platformGoodsCode}
            </foreach>
        </if>
        <if test="platformGoodsIdList != null and platformGoodsIdList.size() != 0">
        and platform_goods_id in
         <foreach collection="platformGoodsIdList" open="(" separator="," close=")" item="platformGoodsId">
            #{platformGoodsId}
        </foreach>
        </if>
        <if test="shopCodes != null  and shopCodes != ''">
            and shop_code in
            <foreach item="shopCode" collection="shopCodes" open="(" separator="," close=")">
                #{shopCode}
            </foreach>
        </if>
        <if test="queryTime != null and queryTime != ''">
            and (create_time &gt;= #{queryTime} or update_time &gt;= #{queryTime})
        </if>

        <!-- 数据范围过滤 -->
        ${params.dataScope}
    </select>

    <select id="selectListingGoodsHeadVOList" parameterType="GoodsHead" resultMap="ListingGoodsHeadVOResult">
        <include refid="selectListingGoodsHeadVo"/>
        WHERE del_flag = 0
        <if test="createBy != null and  createBy != ''">and create_by in
            <foreach item="code" collection="createBy.split(',')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="ids != null  and ids != ''">and id in
            <foreach item="code" collection="ids.split(' ')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>

        <if test="pdmGoodsCode != null  and pdmGoodsCode != ''  and pdmGoodsCode.contains(' '.toString() ) ">and pdm_goods_code in
            <foreach item="code" collection="pdmGoodsCode.split(' ')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="pdmGoodsCode != null  and pdmGoodsCode != '' and !pdmGoodsCode.contains(' '.toString() )">
            and pdm_goods_code like concat( #{pdmGoodsCode}, '%')
        </if>
<!--#         <choose>-->
            <if test="platformGoodsCode != null  and platformGoodsCode != '' and platformGoodsCode.contains(' '.toString() )">
                and platform_goods_code in
                <foreach item="code" collection="platformGoodsCode.split(' ')" open="(" separator="," close=")">
                    #{code}
                </foreach>
            </if>
            <if test="platformGoodsCode != null  and platformGoodsCode != '' and !platformGoodsCode.contains(' '.toString() )">
                and platform_goods_code like concat(  #{platformGoodsCode}, '%')
            </if>
<!--        </choose>-->
        <if test="platformGoodsId != null  and platformGoodsId != ''">and platform_goods_id in
            <foreach item="code" collection="platformGoodsId.split(' ')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="productSeries != null  and productSeries != ''">and product_series = #{productSeries}</if>
        <if test="productModel != null  and productModel != ''">and product_model = #{productModel}</if>
        <if test="platform != null  and platform != ''">and platform = #{platform}</if>
        <if test="publishType != null ">and publish_type = #{publishType}</if>
        <if test="condition != null  and condition != ''">and `condition` = #{condition}</if>
        <if test="title != null  and title != ''">and title like concat('%', #{title}, '%')</if>
        <if test="subtitle != null  and subtitle != ''">and subtitle like concat('%', #{subtitle}, '%')</if>
        <if test="publishStatus != null ">and publish_status like concat(#{publishStatus}, '%') </if>
        <if test="mainImageUrl != null  and mainImageUrl != ''">and main_image_url = #{mainImageUrl}</if>
        <if test="siteCode != null  and siteCode != ''">and site_code in
            <foreach item="code" collection="siteCode.split(',')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="brandCode != null  and brandCode != ''">and brand_code = #{brandCode}</if>
        <if test="shopCode != null  and shopCode != ''">and shop_code = #{shopCode}</if>
        <if test="standardPrice != null and standardPrice !='' ">and standard_price = #{standardPrice}</if>
        <if test="originalPrice != null ">and original_price = #{originalPrice}</if>
        <if test="settlementPrice != null ">and settlement_price = #{settlementPrice}</if>
        <if test="redLinePrice != null ">and red_line_price = #{redLinePrice}</if>
        <if test="settlementPrice != null ">and settlement_price = #{settlementPrice}</if>
        <if test="redLinePrice != null ">and red_line_price = #{redLinePrice}</if>
        <if test="stockOnSalesQty != null ">and stock_on_sales_qty = #{stockOnSalesQty}</if>
        <if test="delFlag != null ">and del_flag = #{delFlag}</if>
        <if test="smcFlag != null ">and smc_flag = #{smcFlag}</if>
        <if test="taskName != null and taskName != ''">and task_name = #{taskName}</if>
        <if test="categoryIds != null and  categoryIds != ''">and category_id in
            <foreach item="code" collection="categoryIds.split(',')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
            and date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
        </if>
        <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
            and date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
        </if>
        <if test="params.beginUpTime != null and params.beginUpTime != ''"><!-- 开始时间检索 -->
            and date_format(update_time,'%y%m%d') &gt;= date_format(#{params.beginUpTime},'%y%m%d')
        </if>
        <if test="params.endUpTime != null and params.endUpTime != ''"><!-- 结束时间检索 -->
            and date_format(update_time,'%y%m%d') &lt;= date_format(#{params.endUpTime},'%y%m%d')
        </if>

        <if test="params.beginOnLineTime != null and params.beginOnLineTime != ''"><!-- 上架时间检索 -->
            and date_format(online_time,'%y%m%d') &gt;= date_format(#{params.beginOnLineTime},'%y%m%d')
        </if>
        <if test="params.endOnLineTime != null and params.endOnLineTime != ''"><!-- 上架结束时间检索 -->
            and date_format(online_time,'%y%m%d') &lt;= date_format(#{params.endOnLineTime},'%y%m%d')
        </if>

        <if test="params.beginOffTime != null and params.beginOffTime != ''"><!-- 上架时间检索 -->
            and date_format(off_time,'%y%m%d') &gt;= date_format(#{params.beginOffTime},'%y%m%d')
        </if>
        <if test="params.endOffTime != null and params.endOffTime != ''"><!-- 上架结束时间检索 -->
            and date_format(off_time,'%y%m%d') &lt;= date_format(#{params.endOffTime},'%y%m%d')
        </if>
        <if test="params.publishDayStart != null"><!-- 刊登天数开始时间检索 -->
            AND DATEDIFF(CURDATE(), online_time) >= #{params.publishDayStart}
        </if>
        <if test="params.publishDayEnd != null "><!-- 刊登天数结束时间检索 -->
            AND DATEDIFF(CURDATE(), online_time) &lt;= #{params.publishDayEnd}
        </if>

        <if test="shopCodes != null  and shopCodes != ''">
            and shop_code in
            <foreach item="shopCode" collection="shopCodes" open="(" separator="," close=")">
                #{shopCode}
            </foreach>
        </if>
        <if test="censorship != null and  censorship != ''">and censorship in
            <foreach item="code" collection="censorship.split(',')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="redLinePriceFlag != null and redLinePriceFlag != '' and redLinePriceFlag == 0"><!-- 售价范围 -->
            and settlement_price &lt;  red_line_price
        </if>
        <if test="redLinePriceFlag != null and redLinePriceFlag != '' and redLinePriceFlag == 1 "><!-- 售价范围 -->
            and settlement_price &gt;= red_line_price
        </if>
        <if test="standardPriceStart != null and standardPriceStart != ''"><!-- 售价范围 -->
            and standard_price &gt;=  ${standardPriceStart}
        </if>
        <if test="standardPriceEnd != null and standardPriceEnd != ''"><!-- 售价范围 -->
            and standard_price &lt;=  ${standardPriceEnd}
        </if>
        <if test="stockOnSalesQtyStart != null"><!-- 库存范围 -->
            and stock_on_sales_qty &gt;= #{stockOnSalesQtyStart}
        </if>
        <if test="stockOnSalesQtyEnd != null "><!-- 库存范围 -->
            and stock_on_sales_qty &lt;= #{stockOnSalesQtyEnd}
        </if>

        <choose>
            <when test="adaptationStatus != null and adaptationStatus != '' and adaptationStatus == '待适配'">
                and (adaptation_status is null or adaptation_status ='待适配')
            </when>
            <when test="adaptationStatus != null and adaptationStatus != '' and adaptationStatus != '待适配'">
                and adaptation_status = #{adaptationStatus}
            </when>
        </choose>


    <if test="adaptationStatusList != null and adaptationStatusList.size() != 0">
            and adaptation_status in
            <foreach item="adaptationStatus" collection="adaptationStatusList" open="(" separator="," close=")">
                #{adaptationStatus}
            </foreach>
        </if>
        <if test="publishTypeList != null and publishTypeList.size() != 0">
        and publish_type in
         <foreach collection="publishTypeList" open="(" separator="," close=")" item="publishType">
            #{publishType}
        </foreach>
        </if>
        <if test="smcFlagList != null and smcFlagList.size() != 0">
            and smc_flag in
            <foreach collection="smcFlagList" open="(" separator="," close=")" item="smcFlag">
                #{smcFlag}
            </foreach>
        </if>
        <if test="brandCodeList != null and brandCodeList.size() != 0">
        and brand_code in
            <foreach collection="brandCodeList" open="(" separator="," close=")" item="brandCode">
                #{brandCode}
            </foreach>
        </if>
        <if test="skuRateLabel != null and skuRateLabel != ''">and sku_rate_label in
            <foreach item="code" collection="skuRateLabel.split(',')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="listingRateLabel != null and listingRateLabel != ''">and listing_rate_label in
            <foreach item="code" collection="listingRateLabel.split(',')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="isMain != null ">
            <if test="isMain == 0">
                and (is_main= 0 or is_main is null)
            </if>
            <if test="isMain == 1">
                and is_main= 1
            </if>
        </if>
        <!-- 数据范围过滤 -->
        ${params.dataScope}
    </select>

    <select id="selectListingGoodsHeadVOListCount" parameterType="GoodsHead" resultMap="ListingGoodsHeadVOResult">
        <include refid="selectListingGoodsHeadVoCount"/>
        WHERE del_flag = 0
        <if test="createBy != null and  createBy != ''">and create_by in
            <foreach item="code" collection="createBy.split(',')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="ids != null  and ids != ''">and id in
            <foreach item="code" collection="ids.split(' ')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>

        <if test="pdmGoodsCode != null  and pdmGoodsCode != '' and pdmGoodsCode.contains(' '.toString() )">and pdm_goods_code in
            <foreach item="code" collection="pdmGoodsCode.split(' ')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="pdmGoodsCode != null  and pdmGoodsCode != '' and !pdmGoodsCode.contains(' '.toString() )">
            and pdm_goods_code like concat( #{pdmGoodsCode}, '%')
        </if>
        <if test="productSeries != null  and productSeries != ''">and product_series = #{productSeries}</if>
        <if test="productModel != null  and productModel != ''">and product_model = #{productModel}</if>
        <!--#         <choose>-->
        <if test="platformGoodsCode != null  and platformGoodsCode != '' and platformGoodsCode.contains(' '.toString() )">
            and platform_goods_code in
            <foreach item="code" collection="platformGoodsCode.split(' ')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="platformGoodsCode != null  and platformGoodsCode != '' and !platformGoodsCode.contains(' '.toString() )">
            and platform_goods_code like concat(  #{platformGoodsCode}, '%')
        </if>
        <!--        </choose>-->
        <if test="platformGoodsId != null  and platformGoodsId != ''">and platform_goods_id in
            <foreach item="code" collection="platformGoodsId.split(' ')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="platform != null  and platform != ''">and platform = #{platform}</if>
        <if test="publishType != null ">and publish_type = #{publishType}</if>
        <if test="condition != null  and condition != ''">and `condition` = #{condition}</if>
        <if test="title != null  and title != ''">and title like concat('%', #{title}, '%')</if>
        <if test="subtitle != null  and subtitle != ''">and subtitle like concat('%', #{subtitle}, '%')</if>
        <if test="publishStatus != null ">and publish_status like concat(#{publishStatus}, '%') </if>
        <if test="mainImageUrl != null  and mainImageUrl != ''">and main_image_url = #{mainImageUrl}</if>
        <if test="siteCode != null  and siteCode != ''">and site_code in
            <foreach item="code" collection="siteCode.split(',')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="brandCode != null  and brandCode != ''">and brand_code = #{brandCode}</if>
        <if test="standardPrice != null  and standardPrice !=''">and standard_price = #{standardPrice}</if>
        <if test="originalPrice != null ">and original_price = #{originalPrice}</if>
        <if test="settlementPrice != null ">and settlement_price = #{settlementPrice}</if>
        <if test="redLinePrice != null ">and red_line_price = #{redLinePrice}</if>
        <if test="stockOnSalesQty != null ">and stock_on_sales_qty = #{stockOnSalesQty}</if>
        <if test="delFlag != null ">and del_flag = #{delFlag}</if>
        <if test="smcFlag != null ">and smc_flag = #{smcFlag}</if>
        <if test="taskName != null and taskName != ''">and task_name = #{taskName}</if>
        <if test="categoryIds != null and  categoryIds != ''">and category_id in
            <foreach item="code" collection="categoryIds.split(',')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
            and date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
        </if>
        <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
            and date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
        </if>
        <if test="params.beginUpTime != null and params.beginUpTime != ''"><!-- 开始时间检索 -->
            and date_format(update_time,'%y%m%d') &gt;= date_format(#{params.beginUpTime},'%y%m%d')
        </if>
        <if test="params.endUpTime != null and params.endUpTime != ''"><!-- 结束时间检索 -->
            and date_format(update_time,'%y%m%d') &lt;= date_format(#{params.endUpTime},'%y%m%d')
        </if>
        <if test="params.beginOnLineTime != null and params.beginOnLineTime != ''"><!-- 上架时间检索 -->
            and date_format(online_time,'%y%m%d') &gt;= date_format(#{params.beginOnLineTime},'%y%m%d')
        </if>
        <if test="params.endOnLineTime != null and params.endOnLineTime != ''"><!-- 上架结束时间检索 -->
            and date_format(online_time,'%y%m%d') &lt;= date_format(#{params.endOnLineTime},'%y%m%d')
        </if>

        <if test="params.beginOffTime != null and params.beginOffTime != ''"><!-- 上架时间检索 -->
            and date_format(off_time,'%y%m%d') &gt;= date_format(#{params.beginOffTime},'%y%m%d')
        </if>
        <if test="params.endOffTime != null and params.endOffTime != ''"><!-- 上架结束时间检索 -->
            and date_format(off_time,'%y%m%d') &lt;= date_format(#{params.endOffTime},'%y%m%d')
        </if>
        <if test="params.publishDayStart != null"><!-- 刊登天数开始时间检索 -->
            AND DATEDIFF(CURDATE(), online_time) >= #{params.publishDayStart}
        </if>
        <if test="params.publishDayEnd != null"><!-- 刊登天数结束时间检索 -->
            AND DATEDIFF(CURDATE(), online_time) &lt;= #{params.publishDayEnd}
        </if>
        <if test="standardPriceStart != null and standardPriceStart != ''"><!-- 售价范围 -->
            and standard_price &gt;= ${standardPriceStart}
        </if>
        <if test="standardPriceEnd != null and standardPriceEnd != ''"><!-- 售价范围 -->
            and standard_price &lt;= ${standardPriceEnd}
        </if>
        <if test="stockOnSalesQtyStart != null"><!-- 库存范围 -->
            and stock_on_sales_qty &gt;= #{stockOnSalesQtyStart}
        </if>
        <if test="stockOnSalesQtyEnd != null "><!-- 库存范围 -->
            and stock_on_sales_qty &lt;= #{stockOnSalesQtyEnd}
        </if>
        <if test="redLinePriceFlag != null and redLinePriceFlag != '' and redLinePriceFlag == 0"><!-- 售价范围 -->
            and settlement_price &lt;  red_line_price
        </if>
        <if test="redLinePriceFlag != null and redLinePriceFlag != '' and redLinePriceFlag == 1 "><!-- 售价范围 -->
            and settlement_price &gt;= red_line_price
        </if>
        <if test="shopCodes != null  and shopCodes != ''">
            and shop_code in
            <foreach item="shopCode" collection="shopCodes" open="(" separator="," close=")">
                #{shopCode}
            </foreach>
        </if>
        <if test="censorship != null and  censorship != ''">and censorship in
            <foreach item="code" collection="censorship.split(',')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>

        <choose>
            <when test="adaptationStatus != null and adaptationStatus != '' and adaptationStatus == '待适配'">
                and (adaptation_status is null or adaptation_status ='待适配')
            </when>
            <when test="adaptationStatus != null and adaptationStatus != '' and adaptationStatus != '待适配'">
                and adaptation_status = #{adaptationStatus}
            </when>
        </choose>

        <if test="adaptationStatusList != null and adaptationStatusList.size() != 0">
            and adaptation_status in
            <foreach item="adaptationStatus" collection="adaptationStatusList" open="(" separator="," close=")">
                #{adaptationStatus}
            </foreach>
        </if>
        <if test="publishTypeList != null and publishTypeList.size() != 0">
            and publish_type in
            <foreach collection="publishTypeList" open="(" separator="," close=")" item="publishType">
                #{publishType}
            </foreach>
        </if>
        <if test="smcFlagList != null and smcFlagList.size() != 0">
            and smc_flag in
            <foreach collection="smcFlagList" open="(" separator="," close=")" item="smcFlag">
                #{smcFlag}
            </foreach>
        </if>

        <if test="brandCodeList != null and brandCodeList.size() != 0">
            and brand_code in
            <foreach collection="brandCodeList" open="(" separator="," close=")" item="brandCode">
                #{brandCode}
            </foreach>
        </if>
        <if test="skuRateLabel != null and skuRateLabel != ''">and sku_rate_label in
            <foreach item="code" collection="skuRateLabel.split(',')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="listingRateLabel != null and listingRateLabel != ''">and listing_rate_label in
            <foreach item="code" collection="listingRateLabel.split(',')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="isMain != null ">
            <if test="isMain == 0">
                and (is_main= 0 or is_main is null)
            </if>
            <if test="isMain == 1">
                and is_main= 1
            </if>        </if>
        GROUP BY publish_status
    </select>

    <select id="selectListingGoodsHeadById" parameterType="Integer" resultMap="ListingGoodsHeadResult">
        <include refid="selectListingGoodsHeadVo"/>
        where id = #{id}
    </select>

    <select id="selectShopCodeById" parameterType="Integer" resultType="string">
        SELECT shop_code
        FROM sc_smc_listing_goods_head
        WHERE id = #{id}
    </select>

    <select id="selectListingGoodsHeadByIds" parameterType="Integer" resultMap="ListingGoodsHeadResult">
        <include refid="selectListingGoodsHeadVo"/>
        WHERE `id` IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertListingGoodsHead" parameterType="GoodsHead" useGeneratedKeys="true" keyProperty="id">
        insert into sc_smc_listing_goods_head
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="pdmGoodsCode != null">pdm_goods_code,</if>
            <if test="platformGoodsCode != null">platform_goods_code,</if>
            <if test="platformGoodsId != null">platform_goods_id,</if>
            <if test="platform != null">platform,</if>
            <if test="shopCode != null">shop_code,</if>
            <if test="publishType != null">publish_type,</if>
            <if test="condition != null">`condition`,</if>
            <if test="title != null">title,</if>
            <if test="subtitle != null">subtitle,</if>
            <if test="adaptationStatus != null">adaptation_status,</if>
            <if test="publishStatus != null">publish_status,</if>
            <if test="mainImageUrl != null">main_image_url,</if>
            <if test="siteCode != null">site_code,</if>
            <if test="brandCode != null">brand_code,</if>
            <if test="standardPrice != null">standard_price,</if>
            <if test="originalPrice != null">original_price,</if>
            <if test="settlementPrice != null">settlement_price,</if>
            <if test="redLinePrice != null">red_line_price,</if>
            <if test="stockOnSalesQty != null">stock_on_sales_qty,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="onlineTime != null ">online_time,</if>
            <if test="offTime != null ">off_time,</if>
            <if test="remark != null">remark,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="taskName != null">task_name,</if>
            <if test="smcFlag != null ">smc_flag,</if>
            <if test="categoryId != null ">category_id,</if>
            <if test="censorship != null ">censorship,</if>
            <if test="productSeries != null ">product_series,</if>
            <if test="productModel != null ">product_model,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="pdmGoodsCode != null">#{pdmGoodsCode},</if>
            <if test="platformGoodsCode != null">#{platformGoodsCode},</if>
            <if test="platformGoodsId != null">#{platformGoodsId},</if>
            <if test="platform != null">#{platform},</if>
            <if test="shopCode != null">#{shopCode},</if>
            <if test="publishType != null">#{publishType},</if>
            <if test="condition != null">#{condition},</if>
            <if test="title != null">#{title},</if>
            <if test="subtitle != null">#{subtitle},</if>
            <if test="adaptationStatus != null">#{adaptationStatus},</if>
            <if test="publishStatus != null">#{publishStatus},</if>
            <if test="mainImageUrl != null">#{mainImageUrl},</if>
            <if test="siteCode != null">#{siteCode},</if>
            <if test="brandCode != null">#{brandCode},</if>
            <if test="standardPrice != null">#{standardPrice},</if>
            <if test="originalPrice != null">#{originalPrice},</if>
            <if test="settlementPrice != null">#{settlementPrice},</if>
            <if test="redLinePrice != null">#{redLinePrice},</if>
            <if test="stockOnSalesQty != null">#{stockOnSalesQty},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="onlineTime != null ">#{onlineTime},</if>
            <if test="offTime != null ">#{offTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="taskName != null">#{taskName},</if>
            <if test="smcFlag != null ">#{smcFlag},</if>
            <if test="categoryId != null">#{categoryId},</if>
            <if test="censorship != null">#{censorship},</if>
            <if test="productSeries != null">#{productSeries},</if>
            <if test="productModel != null">#{productModel},</if>
        </trim>
    </insert>

    <insert id="insertListingGoodsHeads">
        INSERT INTO sc_smc_listing_goods_head (pdm_goods_code, platform_goods_code, platform_goods_id, platform,
        shop_code, publish_type, `condition`, title, subtitle, publish_status, main_image_url, site_code, brand_code
        ,standard_price,original_price, stock_on_sales_qty, create_by, update_by, remark, del_flag, category_id, product_series,product_model)
        VALUES
        <foreach collection="goodsHeadList" separator="," item="goodsHead">
            (#{goodsHead.pdmGoodsCode}, #{goodsHead.platformGoodsCode}, #{goodsHead.platformGoodsId},
            #{goodsHead.platform},
            #{goodsHead.shopCode},#{goodsHead.publishType}, #{goodsHead.condition}, #{goodsHead.title},
            #{goodsHead.subtitle},
            #{goodsHead.publishStatus},#{goodsHead.mainImageUrl}, #{goodsHead.siteCode}, #{goodsHead.brandCode},
             #{goodsHead.standardPrice},#{goodsHead.originalPrice}, #{goodsHead.stockOnSalesQty}, #{goodsHead.createBy},
            #{goodsHead.updateBy},
            #{goodsHead.remark}, #{goodsHead.del_flag}, #{goodsHead.category_id}, #{goodsHead.productSeries}, #{goodsHead.productModel}
            )
        </foreach>
    </insert>

    <insert id="insertScAsinList">
        insert IGNORE into temp_asin (asin) values
        <foreach collection="asinList" separator="," item="asin">
            (#{asin})
        </foreach>
    </insert>

    <update id="updateListingGoodsHead" parameterType="GoodsHead">
        update sc_smc_listing_goods_head
        <trim prefix="SET" suffixOverrides=",">
            <if test="pdmGoodsCode != null">pdm_goods_code = #{pdmGoodsCode},</if>
            <if test="platformGoodsCode != null">platform_goods_code = #{platformGoodsCode},</if>
            <if test="platformGoodsId != null">platform_goods_id = #{platformGoodsId},</if>
            <if test="platform != null">platform = #{platform},</if>
            <if test="shopCode != null">shop_code = #{shopCode},</if>
            <if test="publishType != null">publish_type = #{publishType},</if>
            <if test="condition != null">`condition` = #{condition},</if>
            <if test="title != null">title = #{title},</if>
            <if test="subtitle != null">subtitle = #{subtitle},</if>
            <if test="publishStatus != null">publish_status = #{publishStatus},</if>
            <if test="adaptationStatus != null">adaptation_status = #{adaptationStatus},</if>
            <if test="mainImageUrl != null">main_image_url = #{mainImageUrl},</if>
            <if test="siteCode != null">site_code = #{siteCode},</if>
            <if test="brandCode != null">brand_code = #{brandCode},</if>
            <if test="standardPrice != null">standard_price = #{standardPrice},</if>
            <if test="originalPrice != null">original_price = #{originalPrice},</if>
            <if test="settlementPrice != null">settlement_price = #{settlementPrice},</if>
            <if test="redLinePrice != null">red_line_price = #{redLinePrice},</if>
            <if test="stockOnSalesQty != null">stock_on_sales_qty = #{stockOnSalesQty},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="publishingHandler != null ">publishing_handler = #{publishingHandler},</if>
            <if test="pdmStatus != null ">pdm_status = #{pdmStatus},</if>
            <if test="onlineTime != null ">online_time = #{onlineTime},</if>
            <if test="offTime != null ">off_time = #{offTime},</if>
            <if test="censorship != null ">censorship = #{censorship},</if>
            <if test="smcFlag != null ">smc_flag = #{smcFlag},</if>
            <if test="productSeries != null ">product_series = #{productSeries},</if>
            <if test="productModel != null ">product_model = #{productModel},</if>
            <if test="asinNullFlag != null and asinNullFlag==1">
                platform_goods_id=null,
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteListingGoodsHeadById" parameterType="Integer">
        delete
        from sc_smc_listing_goods_head
        where id = #{id}
    </delete>

    <delete id="deleteListingGoodsHeadByIds" parameterType="integer">
        delete from sc_smc_listing_goods_head where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="deleteListingGoodsHeadByIdList" parameterType="integer">
        UPDATE sc_smc_listing_goods_head SET `del_flag` = 2 WHERE `id` IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="stopPublishListingByIds" parameterType="integer">
        UPDATE sc_smc_listing_goods_head SET `publish_status` = '5' WHERE `id` IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="publishListingByIds" parameterType="integer">
        UPDATE sc_smc_listing_goods_head SET `publish_status` = '1',publishing_handler=null WHERE `id` IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!--获取商品数据Map集合-->
    <select id="selectGoodsHeadMapById" parameterType="integer" resultType="map">
        <include refid="selectListingGoodsHeadVo"/>
        where id = #{id}
    </select>



    <select id="selectAmGoodsHeads" parameterType="GoodsHead" resultMap="ListingGoodsHeadVOResult">
        <include refid="selectListingGoodsHeadVo"/>
        WHERE del_flag = 0 and (platform_goods_id is null or platform_goods_id='')
        <if test="pdmGoodsCode != null  and pdmGoodsCode != ''">and pdm_goods_code = #{pdmGoodsCode}</if>
        <if test="platformGoodsCode != null  and platformGoodsCode != ''">and platform_goods_code =
            #{platformGoodsCode}
        </if>
        <if test="productSeries != null  and productSeries != ''">and product_series = #{productSeries}</if>
        <if test="productModel != null  and productModel != ''">and product_model = #{productModel}</if>
        <if test="shopCode != null  and shopCode != ''">and shop_code = #{shopCode}</if>
        <if test="platform != null  and platform != ''">and platform = #{platform}</if>
        <if test="publishType != null  and publishType != ''">and publish_type = #{publishType}</if>
        <if test="condition != null  and condition != ''">and `condition` = #{condition}</if>
        <if test="title != null  and title != ''">and title like concat('%', #{title}, '%')</if>
        <if test="subtitle != null  and subtitle != ''">and subtitle like concat('%', #{subtitle}, '%')</if>
        <if test="publishStatus != null ">and publish_status = '${publishStatus}'</if>
        <if test="mainImageUrl != null  and mainImageUrl != ''">and main_image_url = #{mainImageUrl}</if>
        <if test="siteCode != null  and siteCode != ''">and site_code = #{siteCode}</if>
        <if test="brandCode != null  and brandCode != ''">and brand_code = #{brandCode}</if>
        <if test="standardPrice != null ">and standard_price = #{standardPrice}</if>
        <if test="originalPrice != null ">and original_price = #{originalPrice}</if>
        <if test="stockOnSalesQty != null ">and stock_on_sales_qty = #{stockOnSalesQty}</if>
        <if test="delFlag != null ">and del_flag = #{delFlag}</if>
        <if test="categoryId != null ">and category_id = #{categoryId}</if>
        <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
            and date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
        </if>
        <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
            and date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
        </if>
        <if test="onlineTime != null ">and #{onlineTime} &lt;= online_time  </if>

        <if test="publishStatusList != null and publishStatusList.size() != 0">
        and publish_status in
         <foreach collection="publishStatusList" open="(" separator="," close=")" item="publishStatus">
             '${publishStatus}'
        </foreach>
        </if>

        <!-- 数据范围过滤 -->
        ${params.dataScope}
    </select>


    <select id="selectListingByPdmGoodsCode" resultMap="ListingGoodsHeadResult">
        <include refid="selectListingGoodsHeadVo"/>
         WHERE  (`publish_status` = 2 or `publish_status` = 4)  and stock_on_sales_qty > 0 and `pdm_goods_code` IN
        <foreach item="code" collection="codes" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>


        <update id="updateListingByIds">
        UPDATE sc_smc_listing_goods_head SET `publish_status` = 3,stock_on_sales_qty= 0  WHERE  (`publish_status` = 2 or `publish_status` = 4)  and stock_on_sales_qty > 0 and `id` IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

     <update id="updateListingPublishStatusByIds">
        UPDATE sc_smc_listing_goods_head SET `publish_status` = 3  WHERE  `id` IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
</update>

    <select id="getListingGoodsHeadVOList"  parameterType="GoodsHead" resultMap="ListingGoodsHeadVOResult">
        <include refid="selectListingGoodsHeadVo"/>
        <where>
         del_flag = 0
        <if test="publishStatus != null ">and publish_status ='${publishStatus}' </if>
        <if test="publishStatusList != null and publishStatusList.size() != 0">
        and publish_status in
         <foreach collection="publishStatusList" open="(" separator="," close=")" item="publishStatus">
             '${publishStatus}'
        </foreach>
        </if>
        <if test="platform != null  and platform != ''">and platform = #{platform}</if>
        <if test="shopCode != null  and shopCode != ''">and shop_code = #{shopCode}</if>
        <if test="skuList != null and skuList.size() != 0">
                and pdm_goods_code in
                <foreach item="sku" collection="skuList" open="(" separator="," close=")">
                    #{sku}
                </foreach>
        </if>
        </where>
    </select>

    <select id="selectCountByTitle" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM
            sc_smc_listing_goods_head
        WHERE
            del_flag = 0
        AND platform = #{platform}
        AND shop_code = #{shopCode}
        AND title = #{title}
        <if test="publishStatusList != null and publishStatusList.size() != 0">
        and publish_status in
         <foreach collection="publishStatusList" open="(" separator="," close=")" item="publishStatus">
             '${publishStatus}'
        </foreach>
        </if>
    </select>

    <select id="getGroundingListing" resultMap="homePageList">
        SELECT
            platform,
            COUNT(*) AS groundingListing,
            SUBSTR( online_time, 1, 10 ) online_time
        FROM
            sc_smc_listing_goods_head
        <where>
            del_flag=0
            and
            SUBSTR( online_time, 1, 10 ) = SUBSTR( SUBDATE( SYSDATE(), INTERVAL 1 DAY ), 1, 10 )
            <if test="idList != null and idList.size() != 0">
               and create_by in
                <foreach collection="idList" open="(" separator="," close=")" item="id">
                    #{id}
                </foreach>
            </if>
        </where>
        GROUP BY
            platform
    </select>

    <select id="getSalesingListing" resultMap="homePageList">
        SELECT
            platform,
            COUNT(*) AS salesingListing
        FROM
            sc_smc_listing_goods_head
        WHERE
            del_flag = 0
          AND  publish_status IN ( '2', '3', '4' )
        <if test="idList != null and idList.size() != 0">
            and create_by in
            <foreach collection="idList" open="(" separator="," close=")" item="id">
                #{id}
            </foreach>
        </if>
        GROUP BY
            platform
    </select>

    <select id="getOffListing" resultMap="homePageList">
        SELECT
            sum( a.offEb ) offEB,
            sum( a.offAm ) offAM
        FROM
            (
                SELECT
                    0 AS offEb,
                    count(*) AS offAm
                FROM
                    sc_smc_listing_goods_head
                WHERE
                        SUBSTR( off_time, 1, 10 ) = SUBSTR( SUBDATE( SYSDATE(), INTERVAL 1 DAY ), 1, 10 )
                  AND del_flag = 2
                  AND platform = 'AM'
                    <if test="idList != null and idList.size() != 0">
                        and create_by in
                        <foreach collection="idList" open="(" separator="," close=")" item="id">
                            #{id}
                        </foreach>
                    </if>


                UNION ALL

                SELECT
                    count(*) AS offEb,
                    0 AS offAm
                FROM
                    sc_smc_listing_goods_head
                WHERE
                        SUBSTR( off_time, 1, 10 ) = SUBSTR( SUBDATE( SYSDATE(), INTERVAL 1 DAY ), 1, 10 )
                  AND del_flag = 0
                  AND publish_status = '6'
                  AND platform = 'EB'
                <if test="idList != null and idList.size() != 0">
                    and create_by in
                     <foreach collection="idList" open="(" separator="," close=")" item="id">
                        #{id}
                     </foreach>
                </if>
            )a
    </select>
    <select id="selectCountByAutoSku" resultType="java.lang.Integer">
      SELECT
            count(1)
        FROM
            sc_smc_listing_goods_head
        WHERE
            del_flag = 0
        AND platform = #{platform}
        AND shop_code = #{shopCode}
        AND smc_flag = #{smcFlag}
        <if test="pdmGoodsCode != null  and pdmGoodsCode != ''">and pdm_goods_code = #{pdmGoodsCode}</if>
        <if test="publishStatusList != null and publishStatusList.size() != 0">
        and publish_status in
         <foreach collection="publishStatusList" open="(" separator="," close=")" item="publishStatus">
             '${publishStatus}'
        </foreach>
       </if>
    </select>

    <update id="updateListingGoodsHeadByIdList">
        update sc_smc_listing_goods_head
        <trim prefix="SET" suffixOverrides=",">
            <if test="pdmGoodsCode != null">pdm_goods_code = #{pdmGoodsCode},</if>
            <if test="platformGoodsCode != null">platform_goods_code = #{platformGoodsCode},</if>
            <if test="platformGoodsId != null">platform_goods_id = #{platformGoodsId},</if>
            <if test="platform != null">platform = #{platform},</if>
            <if test="shopCode != null">shop_code = #{shopCode},</if>
            <if test="publishType != null">publish_type = #{publishType},</if>
            <if test="condition != null">`condition` = #{condition},</if>
            <if test="title != null">title = #{title},</if>
            <if test="subtitle != null">subtitle = #{subtitle},</if>
            <if test="publishStatus != null">publish_status = #{publishStatus},</if>
            <if test="adaptationStatus != null">adaptation_status = #{adaptationStatus},</if>
            <if test="mainImageUrl != null">main_image_url = #{mainImageUrl},</if>
            <if test="siteCode != null">site_code = #{siteCode},</if>
            <if test="brandCode != null">brand_code = #{brandCode},</if>
            <if test="standardPrice != null">standard_price = #{standardPrice},</if>
            <if test="originalPrice != null">original_price = #{originalPrice},</if>
            <if test="stockOnSalesQty != null">stock_on_sales_qty = #{stockOnSalesQty},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="publishingHandler != null ">publishing_handler = #{publishingHandler},</if>
            <if test="pdmStatus != null ">pdm_status = #{pdmStatus},</if>
            <if test="onlineTime != null ">online_time = #{onlineTime},</if>
            <if test="offTime != null ">off_time = #{offTime},</if>
        </trim>
        where id in
        <foreach collection="idList" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </update>

    <select id="selectOnlineListingByPlatfromAndShopCode"   resultMap="ListingGoodsHeadResult">
        <include refid="selectListingGoodsHeadVo"/>
        WHERE del_flag = 0 and platform = #{platform} and shop_code = #{shopCode} and platform_goods_id is not null and platform_goods_id !=''
    </select>


    <select id="selectListBySkuList" parameterType="GoodsHead" resultMap="ListingGoodsHeadVOResult">
      <include refid="selectListingGoodsHeadVo"/>
        <where>
         del_flag = 0
        <if test="publishStatus != null ">and publish_status ='${publishStatus}' </if>
        <if test="publishStatusList != null and publishStatusList.size() != 0">
        and publish_status in
         <foreach collection="publishStatusList" open="(" separator="," close=")" item="publishStatus">
             '${publishStatus}'
        </foreach>
        </if>
        <if test="publishTypeList != null and publishTypeList.size() != 0">
                and publish_type in
                <foreach collection="publishTypeList" open="(" separator="," close=")" item="publishType">
                    #{publishType}
                </foreach>
         </if>
         <if test="platform != null  and platform != ''">and platform = #{platform}</if>
         <if test="shopCode != null  and shopCode != ''">and shop_code = #{shopCode}</if>
            <if test="siteCode != null  and siteCode != ''">and site_code = #{siteCode}</if>
         <if test="skuList != null and skuList.size() != 0">
         and pdm_goods_code in
         <foreach item="sku" collection="skuList" open="(" separator="," close=")">
            #{sku}
         </foreach>
         </if>
        </where>
    </select>

    <select id="selectGoodsCodeSale" resultType="java.lang.String">
        SELECT DISTINCT pdm_goods_code from sc_smc_listing_goods_head  where del_flag=0 and publish_status in('2','3','4') and create_by = #{userName}
    </select>


    <select id="selectAllGoodsCode" resultType="java.lang.String">
        SELECT DISTINCT pdm_goods_code from sc_smc_listing_goods_head  where del_flag=0  and pdm_goods_code !=""
    </select>

    <update id="updateListingRemarkByIds">
         UPDATE sc_smc_listing_goods_head SET `del_flag` = 2 ,remark = #{remark} WHERE `id` IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="clearPlatformGoodId">
        UPDATE sc_smc_listing_goods_head
        SET platform_goods_id = null,
            publish_status    = #{publishStatus},
            online_time       = null,
            off_time          = null
        WHERE `id` = #{id}
    </update>

    <select id="listingGoodsHeadVOList" resultType="com.suncent.smc.persistence.publication.domain.vo.GoodsHeadVO"  resultMap="ListingGoodsHeadVOResult">
        <include refid="selectListingGoodsHeadVo"/>
        WHERE `id` IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectSaleAndOrderList" resultType="java.lang.Integer">
        SELECT he.id from sc_smc_listing_goods_head he
        JOIN sc_smc_auto_sales_detail de on de.platform_sale_code=he.platform_goods_id
        where  he.del_flag=0 and dimension=1 and date(de.period_id_d)=date(SYSDATE())
        <if test="shopCodeList != null and shopCodeList.size() != 0">
            and he.shop_code in
            <foreach collection="shopCodeList" open="(" separator="," close=")" item="shopcode">
                #{shopcode}
            </foreach>
        </if>
        <if test="shopCode != null and shopCode != ''">
                and he.shop_code in
                <foreach item="shopcode" collection="shopCode.split(',')" open="(" separator="," close=")">
                    #{shopcode}
                </foreach>
        </if>
        <choose>
            <when test="orderType != null">
                <choose >
                    <when test="orderType==1">
                        <if test="orderValueStart != null and orderValueStart != ''">
                            and order_quantity_30 >=#{orderValueStart}
                        </if>
                        <if test="orderValueEnd != null and orderValueEnd != ''">
                            and order_quantity_30 &lt;= #{orderValueEnd}
                        </if>
                    </when>
                    <when test="orderType==2">
                        <if test="orderValueStart != null and orderValueStart != ''">
                            and order_quantity_60 >=#{orderValueStart}
                        </if>
                        <if test="orderValueEnd != null and orderValueEnd != ''">
                            and order_quantity_60 &lt;= #{orderValueEnd}
                        </if>
                    </when>
                    <when test="orderType==3">
                        <if test="orderValueStart != null and orderValueStart != ''">
                            and order_quantity_90 >=#{orderValueStart}
                        </if>
                        <if test="orderValueEnd != null and orderValueEnd != ''">
                            and order_quantity_90 &lt;= #{orderValueEnd}
                        </if>
                    </when>
                </choose>
            </when>
        </choose>
        <choose>
            <when test="saleType != null">
                <choose >
                    <when test="saleType==1">
                        <if test="saleValueStart != null and saleValueStart != ''">
                            and sales_volume_30 >=#{saleValueStart}
                        </if>
                        <if test="saleValueEnd != null and saleValueEnd != ''">
                            and sales_volume_30 &lt;= #{saleValueEnd}
                        </if>
                    </when>
                    <when test="saleType==2">
                        <if test="saleValueStart != null and saleValueStart != ''">
                            and sales_volume_60 >=#{saleValueStart}
                        </if>
                        <if test="saleValueEnd != null and saleValueEnd != ''">
                            and sales_volume_60 &lt;= #{saleValueEnd}
                        </if>
                    </when>
                    <when test="saleType==3">
                        <if test="saleValueStart != null and saleValueStart != ''">
                            and sales_volume_90 >=#{saleValueStart}
                        </if>
                        <if test="saleValueEnd != null and saleValueEnd != ''">
                            and sales_volume_90 &lt;= #{saleValueEnd}
                        </if>
                    </when>
                </choose>
            </when>
        </choose>
    </select>
    <select id="selectNewOnlineTimeByShopCodeAndSize" resultMap="ListingGoodsHeadResult">
        <include refid="selectListingGoodsHeadVo"/>
        WHERE del_flag = 0 and platform = 'EB' and shop_code = #{shopCode} and platform_goods_id is not null and platform_goods_id  != ''
        and publish_status in ('2','3','3001','4')
        <if test="platformItemIds != null and platformItemIds.size() != 0">
            and platform_goods_id in
            <foreach collection="platformItemIds" open="(" separator="," close=")" item="itemId">
                #{itemId}
            </foreach>
        </if>
        ORDER BY online_time desc  limit  #{size}
    </select>
    <select id="selectHeadByShopCodeAndShopCategory" resultMap="ListingGoodsHeadResult">
        select head.id,
               head.pdm_goods_code,
               head.platform_goods_code,
               head.platform_goods_id,
               head.platform,
               head.shop_code,
               head.publish_type,
               head.`condition`,
               head.title,
               head.subtitle,
               head.adaptation_status,
               head.publish_status,
               head.main_image_url,
               head.site_code,
               head.brand_code,
               head.standard_price,
               head.original_price,
               head.settlement_price,
               head.red_line_price,
               head.stock_on_sales_qty,
               head.create_by,
               head.create_time,
               head.update_by,
               head.update_time,
               head.remark,
               head.del_flag,
               head.category_id,
               head.publishing_handler,
               head.online_time,
               head.off_time,
               head.task_name,
               head.smc_flag,
               head.censorship,
               head.pdm_status,
               head.listing_rate_label,
               head.sku_rate_label,
               head.is_main
        from sc_smc_listing_goods_head  head
                LEFT JOIN sc_smc_listing_ebay_line line ON head.id = line.listing_head_id
        WHERE
            head.del_flag = 0
          AND head.platform = "EB"
          AND publish_status in ('2','3','3001','4')
          AND head.shop_code = #{shopCode}
          AND line.first_shop_category = #{shopCategory}
    </select>

    <select id="selectFailListing"
            resultMap="ListingGoodsHeadResult">
        SELECT * FROM sc_smc_listing_goods_head
        WHERE del_flag = 0
        AND publish_status in ('4','7','7001')
        AND update_time &lt;= DATE_SUB(NOW(), INTERVAL 2 WEEK);

    </select>
    <select id="queryLatestListing"
            resultMap="ListingGoodsHeadResult">
        <include refid="selectListingGoodsHeadVo"/>
        WHERE del_flag = 0
        AND (
        (platform = 'AM' AND publish_status IN ('2', '3','3001', '4', '6', '6001', '6002', '6003'))
        OR
        (platform = 'EB' AND publish_status IN ('2', '3','3001', '4'))
        )
        AND pdm_goods_code in
        <foreach item="item" collection="skuList" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND platform_goods_id is not null AND platform_goods_id !=''
        ORDER BY update_time DESC
        limit 1;

    </select>

    <select id="selectListByPlatformGoodsCodes" resultMap="ListingGoodsHeadVOResult">
        <include refid="selectListingGoodsHeadVo"/>
        WHERE del_flag = 0
        <if test="platformGoodsCodes != null  and platformGoodsCodes != ''">
            and platform_goods_code in
            <foreach item="code" collection="platformGoodsCodes" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
    </select>

    <update id="deleteFailListing">
        UPDATE sc_smc_listing_goods_head
        SET del_flag = 2, remark = "刊登失败数据超过一个月自动逻辑删除处理"
        WHERE del_flag = 0
        AND publish_status IN ('8', '8001')
        AND update_time &lt;= DATE_SUB(NOW(), INTERVAL 1 MONTH);

    </update>


    <select id="selectHeadListByShopAndTitle" parameterType="GoodsHead" resultMap="ListingGoodsHeadResult">
        <include refid="selectListingGoodsHeadVo"/>
        WHERE del_flag = 0
        <if test="platform != null  and platform != ''">and platform = #{platform}</if>
        <if test="siteCode != null  and siteCode != ''">and site_code = #{siteCode}</if>
        <if test="shopCode != null  and shopCode != ''">and shop_code = #{shopCode}</if>
        <if test="pdmGoodsCode != null  and pdmGoodsCode != ''">and pdm_goods_code = #{pdmGoodsCode}</if>
        <if test="title != null  and title != ''">and title = #{title}</if>
        <if test="publishType != null  and publishType != ''">and publish_type = #{publishType}</if>
        and platform_goods_id is not null and platform_goods_id !=''
    </select>



    <select id="selectHeadsByPublishStatusAndUpdateTime"  resultMap="ListingGoodsHeadResult">
        <include refid="selectListingGoodsHeadVo"/>
        WHERE del_flag = 0
        <if test="publishStatusIn != null  and publishStatusIn != ''">
            and publish_status in
            <foreach item="publishStatus" collection="publishStatusIn" open="(" separator="," close=")">
                '${publishStatus}'
            </foreach>
        </if>
        <if test="beginTime != null and beginTime != ''">
            and date_format(update_time,'%y%m%d %H%i%s') &gt;= date_format(#{beginTime},'%y%m%d %H%i%s')
        </if>
        <if test="endTime != null and endTime != ''">
            and date_format(update_time,'%y%m%d %H%i%s') &lt;= date_format(#{endTime},'%y%m%d %H%i%s')
        </if>
    </select>

    <select id="selectListInventoryNoZeroBySkuList" parameterType="GoodsHead" resultMap="ListingGoodsHeadVOResult">
        <include refid="selectListingGoodsHeadVo"/>
        <where>
            del_flag = 0 and platform_goods_id is not null and platform_goods_id !=''
            <if test="publishStatus != null ">and publish_status ='${publishStatus}' </if>
            <if test="publishStatusList != null and publishStatusList.size() != 0">
                and publish_status in
                <foreach collection="publishStatusList" open="(" separator="," close=")" item="publishStatus">
                    '${publishStatus}'
                </foreach>
            </if>
            <if test="publishTypeList != null and publishTypeList.size() != 0">
                and publish_type in
                <foreach collection="publishTypeList" open="(" separator="," close=")" item="publishType">
                    #{publishType}
                </foreach>
            </if>
            <if test="platform != null  and platform != ''">and platform = #{platform}</if>
            <if test="shopCode != null  and shopCode != ''">and shop_code = #{shopCode}</if>
            <if test="skuList != null and skuList.size() != 0">
                and pdm_goods_code in
                <foreach item="sku" collection="skuList" open="(" separator="," close=")">
                    #{sku}
                </foreach>
            </if>
            <if test="shopCode != null and shopCode != '' and shopCode != 'VC1'">
                and stock_on_sales_qty > 0
            </if>
        </where>
    </select>
    <select id="selectListingGoodsByShopAndSku" resultMap="ListingGoodsHeadVOResult">
        <include refid="selectListingGoodsHeadVo"/>
        WHERE del_flag = 0
        AND (
        (platform = 'AM' AND publish_status IN ('2', '3','3001', '4', '6', '6001', '6002', '6003'))
        OR
        (platform = 'EB' AND publish_status IN ('2', '3','3001', '4'))
        )
            <if test="userId !=null and userId != 1 ">and create_by = #{userId}</if>
            <if test="shopCodeList != null and shopCodeList.size() != 0">
                and shop_code in
                <foreach collection="shopCodeList" open="(" separator="," close=")" item="shopCode">
                    #{shopCode}
                </foreach>
            </if>
            <if test="skuList != null and skuList.size() != 0">
                and pdm_goods_code in
                <foreach item="sku" collection="skuList" open="(" separator="," close=")">
                    #{sku}
                </foreach>
            </if>
    </select>
    <select id="selectGoodsCodeSaleByShop" resultType="java.lang.String">
        SELECT DISTINCT pdm_goods_code from sc_smc_listing_goods_head  where del_flag=0
        <if test="userId !=null and userId != 1 ">and create_by = #{userId}</if>
        and publish_status in('2','3','4')
        <if test="shopCodeList != null and shopCodeList.size() != 0">
            and shop_code in
            <foreach collection="shopCodeList" open="(" separator="," close=")" item="shopCode">
                #{shopCode}
            </foreach>
        </if>
    </select>
    <select id="selectListingUpdateFailureByShop" resultMap="ListingGoodsHeadVOResult">
        <include refid="selectListingGoodsHeadVo"/>
        WHERE del_flag = 0 and publish_status in('4','8','8001')
        <if test="userId !=null and userId != 1 ">and create_by = #{userId}</if>
        <if test="shopCodeList != null and shopCodeList.size() != 0">
            and shop_code in
            <foreach collection="shopCodeList" open="(" separator="," close=")" item="shopCode">
                #{shopCode}
            </foreach>
        </if>
    </select>
    <select id="selectOnlineListing" resultMap="ListingGoodsHeadVOResult">
        <include refid="selectListingGoodsHeadVo"/>
        WHERE del_flag = 0 and platform = 'EB' and publish_status in('2','3','3001','4')
        <if test="shopCode != null  and shopCode != ''">and shop_code = #{shopCode}</if>
        <if test="skus != null and skus.size() != 0">
            and pdm_goods_code in
            <foreach item="sku" collection="skus" open="(" separator="," close=")">
                #{sku}
            </foreach>
        </if>

    </select>


    <select id="selectAllListing" resultMap="ListingGoodsHeadVOResult">
        select id,
        pdm_goods_code,
        platform_goods_code,
        platform_goods_id,
        platform,
        site_code,
        shop_code,
        category_id,
        create_by,
        create_time,
        update_by,
        update_time
        from sc_smc_listing_goods_head
        WHERE del_flag = 0
        <if test="shopCode != null  and shopCode != ''">and shop_code = #{shopCode}</if>
        <if test="skus != null and skus.size() != 0">
            and pdm_goods_code in
            <foreach item="sku" collection="skus" open="(" separator="," close=")">
                #{sku}
            </foreach>
        </if>
    </select>

    <select id="selectListingZeroQty" resultMap="ListingGoodsHeadResult">
        <include refid="selectListingGoodsHeadVo"/>
        WHERE del_flag = 0
        AND stock_on_sales_qty = 0 and pdm_goods_code !='' and pdm_goods_code is not null
        <if test="platform != null  and platform != ''">and platform = #{platform}</if>
        <if test="shopCode != null  and shopCode != ''">and shop_code = #{shopCode}</if>
        <if test="siteCode != null  and siteCode != ''">and site_code = #{siteCode}</if>
        <if test="platform == 'AM'">
            AND publish_status IN ('2', '3','3001', '4', '6', '6001', '6002', '6003')
            AND publish_type = 0
        </if>
        <if test="platform == 'EB'">
            AND publish_status IN ('2', '3','3001', '4')
        </if>
    </select>

    <select id="selectNotRelationAPlusListing"
            resultType="com.suncent.smc.persistence.aplus.domain.vo.NotRelationAsinVO">
        SELECT
            h.id,
            h.pdm_goods_code pdmGoodsCode,
            h.platform_goods_code platformGoodsCode,
            h.platform_goods_id platformGoodsId,
            h.title,
            h.brand_code brandCode,
            r.resource_url resourceUrl
        FROM
            sc_smc_listing_goods_head h left join sc_smc_listing_goods_resource r on h.id = r.goods_id
        WHERE
            h.platform = 'AM' and h.del_flag = 0
          AND h.platform_goods_id IS NOT NULL
          AND h.platform_goods_id != ''
	AND h.shop_code = #{shopCode}
    AND h.platform_goods_id not in (select asin from sc_smc_aplus_asin where aplus_id = #{aplusId} and relation_mark = 0)
        <if test="asin != null and asin != ''" >
            and h.platform_goods_id like CONCAT('%',#{asin},'%')
        </if>
        <if test="asinList != null and asinList.size() != 0">
            and h.platform_goods_id in
            <foreach collection="asinList" open="(" separator="," close=")" item="asin">
                #{asin}
            </foreach>
        </if>
        <if test="ids != null and ids.length != 0">
            and h.id in
            <foreach collection="ids" open="(" separator="," close=")" item="id">
                #{id}
            </foreach>
        </if>
        <if test="shopCode != null and shopCode != ''">
            and h.shop_code = #{shopCode}
        </if>
        <if test="brandCodeList != null and brandCodeList.size() != 0">
            and h.brand_code in
            <foreach collection="brandCodeList" open="(" separator="," close=")" item="brandCode">
                #{brandCode}
            </foreach>
        </if>
        <if test="categoryIds != null and  categoryIds != ''">and h.category_id in
            <foreach item="code" collection="categoryIds.split(',')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
	AND r.is_main = 1
    </select>

    <select id="selectFailTodoListing" resultMap="ListingGoodsHeadResult">
        select
        h.id,
        h.pdm_goods_code,
        h.platform_goods_code,
        h.platform_goods_id,
        h.platform,
        h.shop_code,
        h.publish_type,
        h.`condition`,
        h.title,
        h.subtitle,
        h.adaptation_status,
        h.publish_status,
        h.main_image_url,
        h.site_code,
        h.brand_code,
        h.standard_price,
        h.original_price,
        h.stock_on_sales_qty,
        u.user_name create_by,
        h.create_time,
        h.update_by,
        h.update_time,
        h.remark,
        h.del_flag,
        h.category_id,
        h.publishing_handler,
        h.online_time,
        h.off_time,
        h.task_name,
        h.smc_flag,
        h.censorship,
        h.pdm_status
        from sc_smc_listing_goods_head h left join sys_user u on h.create_by = u.user_id
        <where>
            and h.del_flag = 0
            <if test="createBy != null and createBy != ''">
                and h.create_by = #{createBy}
            </if>
            <if test="publishStatusList != null and publishStatusList.size() != 0">
                and h.publish_status in
                <foreach collection="publishStatusList" open="(" separator="," close=")" item="publishStatus">
                    #{publishStatus}
                </foreach>
            </if>
            <if test="shopCodes != null and shopCodes.size() != 0">
                and h.shop_code in
                <foreach collection="shopCodes" open="(" separator="," close=")" item="shopCode">
                    #{shopCode}
                </foreach>
            </if>
        </where>
    </select>

    <update id="updataAsinRefundRateLabel">
        UPDATE sc_smc_listing_goods_head
        SET listing_rate_label = #{refundRateLabel}
        WHERE  del_flag = 0 and platform = #{platform} and  platform_goods_code = #{platformSaleCode}
        <if test="platform == 'AM'">
            AND publish_status IN (2, 3,3001, 4, 6, 6001, 6002, 6003)
            AND publish_type = 0
        </if>
        <if test="platform == 'EB'">
            AND publish_status IN (2, 3,3001, 4)
        </if>

    </update>

    <update id="updataSkuRefundRateLabel">
        UPDATE sc_smc_listing_goods_head
        SET sku_rate_label = #{refundRateLabel}
        WHERE  del_flag = 0 and platform = #{platform} and  pdm_goods_code = #{sku}
        <if test="platform == 'AM'">
            AND publish_status IN (2, 3,3001, 4, 6, 6001, 6002, 6003)
            AND publish_type = 0
        </if>
        <if test="platform == 'EB'">
            AND publish_status IN (2, 3,3001, 4)
        </if>
    </update>

    <update id="updateByAMReport">
        update sc_smc_listing_goods_head
        <trim prefix="set" suffixOverrides=",">
            <if test="publishStatus != null">
                publish_status = #{publishStatus},
            </if>
            <if test="price != null and price != ''">
                standard_price = #{price},
            </if>
            <if test="quantity != null and quantity != ''">
                stock_on_sales_qty = #{quantity},
            </if>
        </trim>
        where del_flag = 0 and platform = 'AM' and shop_code = #{shopCode} and publish_status not in (1, 1001, 1002, 1004, 1005, 3, 3001, 5, 9001)
        and platform_goods_code = #{sellerSku}  and platform_goods_id = #{asin}
    </update>

    <select id="countByPublishFailByShopCodes" resultType="int">
        select count(1) from sc_smc_listing_goods_head
        WHERE del_flag = 0
          AND publish_status IN (4, 8)
        <if test="shopCodes != null and shopCodes.size() != 0">
            and shop_code in
            <foreach collection="shopCodes" open="(" separator="," close=")" item="shopCode">
                #{shopCode}
            </foreach>
        </if>
    </select>

    <select id="selectAsinByShopCodeAndAsinIsNotNull" resultType="string">
        select DISTINCT platform_goods_id from sc_smc_listing_goods_head where del_flag = 0 and shop_code = #{shopCode} and platform_goods_id is not null and platform_goods_id != ''
               AND ( (platform = 'AM' AND publish_status IN ('2', '3','3001', '4', '6', '6001', '6002', '6003'))
                    OR
                    (platform = 'EB' AND publish_status IN ('2', '3','3001', '4'))
                )
    </select>

    <select id="selectListingGoodsByShopCodesAndSku" resultMap="ListingGoodsHeadVOResult">
        <include refid="selectListingGoodsHeadVo"/>
        WHERE del_flag = 0
        AND (
        (platform = 'AM' AND publish_status IN ('2', '3','3001', '4', '6', '6001', '6002', '6003'))
        OR
        (platform = 'EB' AND publish_status IN ('2', '3','3001', '4'))
        )
        <if test="shopCodes != null and shopCodes.size() != 0">
            and shop_code in
            <foreach collection="shopCodes" open="(" separator="," close=")" item="shopCode">
                #{shopCode}
            </foreach>
        </if>
        <if test="goodsCode != null  and goodsCode != ''">and pdm_goods_code = #{goodsCode}</if>
        <if test="platformSku != null  and platformSku != ''">and platform_goods_code = #{platformSku}</if>
    </select>

    <select id="countListingGoodsByShopCodesAndSku" resultType="java.lang.Integer">
        SELECT count(1) from sc_smc_listing_goods_head
        WHERE del_flag = 0
        AND (
        (platform = 'AM' AND publish_status IN ('2', '3','3001', '4', '6', '6001', '6002', '6003'))
        OR
        (platform = 'EB' AND publish_status IN ('2', '3','3001', '4'))
        )
        <if test="shopCodes != null and shopCodes.size() != 0">
            and shop_code in
            <foreach collection="shopCodes" open="(" separator="," close=")" item="shopCode">
                #{shopCode}
            </foreach>
        </if>
        <if test="goodsCode != null  and goodsCode != ''">and pdm_goods_code = #{goodsCode}</if>
        <if test="platformSku != null  and platformSku != ''">and platform_goods_code = #{platformSku}</if>
    </select>

    <update id="batchUpdateSettlementPrice">
        <foreach collection="list" item="data" separator=";" >
            update sc_smc_listing_goods_head set settlement_price = #{data.settlementPrice} where id = #{data.id}
        </foreach>
    </update>

    <update id="clearOriginalPrice">
        update sc_smc_listing_goods_head set original_price = null where id = #{id}
    </update>
    <update id="updateOperators">
        update sc_smc_listing_goods_head set create_by = #{operators}, update_by = #{operators}
             where del_flag = 0 and site_code = #{sitCode} and shop_code = #{shopCode}
               and pdm_goods_code = #{goodsCode} and platform_goods_code = #{platformSku}
    </update>

    <update id="batchUpdateSettlementPriceV2">
        update sc_smc_listing_goods_head set settlement_price = standard_price where del_flag=0  and (settlement_price is null) or (platform="EB" and settlement_price != standard_price  )
    </update>


    <delete id="batchUpdateRedLinePrice">
        <foreach collection="list" item="data" separator=";" >
            update sc_smc_listing_goods_head set red_line_price = #{data.redLinePrice} where id = #{data.id}
        </foreach>
    </delete>
    <delete id="deleteTempAsinList">
        delete from temp_asin where asin in <foreach collection="asinList" item="asin" open="(" separator="," close=")">#{asin}</foreach>
    </delete>
    <delete id="deleteTempAsinListAll">
        truncate table temp_asin
    </delete>

    <update id="batchUpdateRedlinePriceV2">
        <foreach collection="list" item="data" separator=";" >
            update sc_smc_listing_goods_head set red_line_price = #{data.redLinePrice} where del_flag=0 and publish_type=#{data.publishType} and pdm_goods_code =#{data.pdmGoodsCode}
        </foreach>
    </update>

    <update id="updateRedLinePrice">
        update sc_smc_listing_goods_head set red_line_price = #{redLinePrice} where del_flag=0 and publish_type=#{publishType} and pdm_goods_code =#{pdmGoodsCode}
    </update>

    <select id="selectPriceTodoListing" resultMap="ListingGoodsHeadResult">
        <include refid="selectListingGoodsHeadVo"/>
        WHERE del_flag = 0
            and publish_status in ('2', '3','3001', '4')
            and settlement_price &lt;  red_line_price
    </select>

    <select id="getGoodsHeadPriceVoList"
            resultType="com.suncent.smc.persistence.publication.domain.vo.GoodsHeadPriceVo">
        select
        id,
        pdm_goods_code goodsCode,
        platform,
        platform_goods_code platformGoodsCode,
        shop_code shopCode,
        standard_price standardPrice,
        original_price originalPrice,
        settlement_price settlementPrice,
        red_line_price redLinePrice,
        category_id categoryId,
        create_by createBy
        from sc_smc_listing_goods_head
        where del_flag = 0
        and publish_status in ('2', '3','3001', '4')
        and pdm_goods_code in
        <foreach collection="list" open="(" separator="," close=")" item="goodsCode">
            #{goodsCode}
        </foreach>
        order by id
    </select>
    <select id="selectListIds" resultType="java.lang.Integer">
        select slgh.id
        from
        <if test="(saleValueStart != null and saleValueStart != '') or (saleValueEnd != null and saleValueEnd != '') or (orderValueStart != null and orderValueStart != '') or (orderValueEnd != null and orderValueEnd != '')">
            sc_smc_auto_sales_detail de
            join sc_smc_listing_goods_head slgh on slgh.platform_goods_id=de.platform_sale_code
        </if>
        <if test="(saleValueStart == null or saleValueStart == '') and (saleValueEnd == null or saleValueEnd == '') and (orderValueStart == null or orderValueStart == '') and (orderValueEnd == null or orderValueEnd == '')">
            sc_smc_listing_goods_head slgh
        </if>
        <if test="pnCode != null and pnCode != ''">
            left join sc_smc_listing_amazon_attribute_line lal on lal.goods_id=slgh.id and lal.table_name='part_number'
        </if>
        <if test="aplusTemplateName != null and aplusTemplateName != ''">
            left join sc_smc_aplus_asin apasin on apasin.asin = slgh.platform_goods_id and apasin.asin != '' and apasin.asin is not null
            left join sc_smc_aplus_description apdesc on apdesc.id = apasin.aplus_id
        </if>
        <if test="listingPerformance != null and listingPerformance != ''">
            left join sc_smc_listing_label sll on sll.head_id=slgh.id
        </if>
        <if test="operationClassificationList != null and operationClassificationList.size() > 0">
            left join `suncent-pdm`.sc_cdp_cate_product cp on cp.category_code = (
            select g.product_category_code from `suncent-pdm`.sc_pdm_goods g where g.goods_code = slgh.pdm_goods_code
            limit 1
            )
        </if>
        <if test="(saleValueStart != null and saleValueStart != '') or (saleValueEnd != null and saleValueEnd != '') or (orderValueStart != null and orderValueStart != '') or (orderValueEnd != null and orderValueEnd != '')">
            where de.platform_sale_code != ''
            AND dimension = '1'
            AND de.period_id_d = #{now}
        </if>
        <if test="(saleValueStart == null or saleValueStart == '') and (saleValueEnd == null or saleValueEnd == '') and (orderValueStart == null or orderValueStart == '') and (orderValueEnd == null or orderValueEnd == '')">
            WHERE slgh.del_flag = 0
        </if>
        <if test="shopCodes != null  and shopCodes != ''">
            and slgh.shop_code in
            <foreach item="shopCode" collection="shopCodes" open="(" separator="," close=")">
                #{shopCode}
            </foreach>
        </if>
        <if test="createBy != null and  createBy != ''">and slgh.create_by in
            <foreach item="code" collection="createBy.split(',')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="ids != null  and ids != ''">and slgh.id in
            <foreach item="code" collection="ids.split(' ')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="productSeries != null  and productSeries != ''">and slgh.product_series = #{productSeries}</if>
        <if test="productModel != null  and productModel != ''">and slgh.product_model = #{productModel}</if>
        <if test="pdmGoodsCode != null  and pdmGoodsCode != ''  and pdmGoodsCode.contains(' '.toString() ) ">and slgh.pdm_goods_code in
            <foreach item="code" collection="pdmGoodsCode.split(' ')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="currentAvailability != null  and currentAvailability != ''"> AND EXISTS (
    SELECT 1 FROM sc_smc_listing_goods_head_detail sghd 
    WHERE sghd.head_id = slgh.id AND sghd.del_flag = 0 and sghd.current_availability = #{currentAvailability}
    )
</if>
        <if test="pdmGoodsCode != null  and pdmGoodsCode != '' and !pdmGoodsCode.contains(' '.toString() )">
            and slgh.pdm_goods_code like concat( #{pdmGoodsCode}, '%')
        </if>
        <!--#         <choose>-->
        <if test="platformGoodsCode != null  and platformGoodsCode != '' and platformGoodsCode.contains(' '.toString() )">
            and slgh.platform_goods_code in
            <foreach item="code" collection="platformGoodsCode.split(' ')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="platformGoodsCode != null  and platformGoodsCode != '' and !platformGoodsCode.contains(' '.toString() )">
            and slgh.platform_goods_code like concat(  #{platformGoodsCode}, '%')
        </if>
        <!--        </choose>-->
        <if test="platformGoodsId != null  and platformGoodsId != ''">and slgh.platform_goods_id in
            <foreach item="code" collection="platformGoodsId.split(' ')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="platform != null  and platform != ''">and slgh.platform = #{platform}</if>
        <if test="publishType != null ">and slgh.publish_type = #{publishType}</if>
        <if test="condition != null  and condition != ''">and slgh.`condition` = #{condition}</if>
        <if test="title != null  and title != ''">and slgh.title like concat('%', #{title}, '%')</if>
        <if test="subtitle != null  and subtitle != ''">and slgh.subtitle like concat('%', #{subtitle}, '%')</if>
        <if test="mainImageUrl != null  and mainImageUrl != ''">and slgh.main_image_url = #{mainImageUrl}</if>
        <if test="siteCode != null  and siteCode != ''">and slgh.site_code in
            <foreach item="code" collection="siteCode.split(',')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="brandCode != null  and brandCode != ''">and slgh.brand_code = #{brandCode}</if>
        <if test="shopCode != null  and shopCode != ''">and slgh.shop_code = #{shopCode}</if>
        <if test="standardPrice != null and standardPrice !='' ">and slgh.standard_price = #{standardPrice}</if>
        <if test="originalPrice != null ">and slgh.original_price = #{originalPrice}</if>
        <if test="settlementPrice != null ">and slgh.settlement_price = #{settlementPrice}</if>
        <if test="redLinePrice != null ">and slgh.red_line_price = #{redLinePrice}</if>
        <if test="settlementPrice != null ">and slgh.settlement_price = #{settlementPrice}</if>
        <if test="redLinePrice != null ">and slgh.red_line_price = #{redLinePrice}</if>
        <if test="stockOnSalesQty != null ">and slgh.stock_on_sales_qty = #{stockOnSalesQty}</if>
        <if test="delFlag != null ">and slgh.del_flag = #{delFlag}</if>
        <if test="smcFlag != null ">and slgh.smc_flag = #{smcFlag}</if>
        <if test="taskName != null and taskName != ''">and slgh.task_name = #{taskName}</if>
        <if test="categoryIds != null and  categoryIds != ''">and slgh.category_id in
            <foreach item="code" collection="categoryIds.split(',')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
            and date_format(slgh.create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
        </if>
        <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
            and date_format(slgh.create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
        </if>
        <if test="params.beginUpTime != null and params.beginUpTime != ''"><!-- 开始时间检索 -->
            and date_format(slgh.update_time,'%y%m%d') &gt;= date_format(#{params.beginUpTime},'%y%m%d')
        </if>
        <if test="params.endUpTime != null and params.endUpTime != ''"><!-- 结束时间检索 -->
            and date_format(slgh.update_time,'%y%m%d') &lt;= date_format(#{params.endUpTime},'%y%m%d')
        </if>

        <if test="params.beginOnLineTime != null and params.beginOnLineTime != ''"><!-- 上架时间检索 -->
            and date_format(slgh.online_time,'%y%m%d') &gt;= date_format(#{params.beginOnLineTime},'%y%m%d')
        </if>
        <if test="params.endOnLineTime != null and params.endOnLineTime != ''"><!-- 上架结束时间检索 -->
            and date_format(slgh.online_time,'%y%m%d') &lt;= date_format(#{params.endOnLineTime},'%y%m%d')
        </if>

        <if test="params.beginOffTime != null and params.beginOffTime != ''"><!-- 上架时间检索 -->
            and date_format(slgh.off_time,'%y%m%d') &gt;= date_format(#{params.beginOffTime},'%y%m%d')
        </if>
        <if test="params.endOffTime != null and params.endOffTime != ''"><!-- 上架结束时间检索 -->
            and date_format(slgh.off_time,'%y%m%d') &lt;= date_format(#{params.endOffTime},'%y%m%d')
        </if>
        <if test="params.publishDayStart != null"><!-- 刊登天数开始时间检索 -->
            AND DATEDIFF(CURDATE(), slgh.online_time) >= #{params.publishDayStart}
        </if>
        <if test="params.publishDayEnd != null "><!-- 刊登天数结束时间检索 -->
            AND DATEDIFF(CURDATE(), slgh.online_time) &lt;= #{params.publishDayEnd}
        </if>

        <if test="censorship != null and  censorship != ''">and slgh.censorship in
            <foreach item="code" collection="censorship.split(',')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="standardPriceStart != null and standardPriceStart != ''"><!-- 售价范围 -->
            and slgh.standard_price &gt;=  ${standardPriceStart}
        </if>
        <if test="standardPriceEnd != null and standardPriceEnd != ''"><!-- 售价范围 -->
            and slgh.standard_price &lt;=  ${standardPriceEnd}
        </if>
        <if test="stockOnSalesQtyStart != null"><!-- 库存范围 -->
            and slgh.stock_on_sales_qty &gt;= #{stockOnSalesQtyStart}
        </if>
        <if test="stockOnSalesQtyEnd != null "><!-- 库存范围 -->
            and slgh.stock_on_sales_qty &lt;= #{stockOnSalesQtyEnd}
        </if>

        <choose>
            <when test="adaptationStatus != null and adaptationStatus != '' and adaptationStatus == '待适配'">
                and (slgh.adaptation_status is null or slgh.adaptation_status ='待适配')
            </when>
            <when test="adaptationStatus != null and adaptationStatus != '' and adaptationStatus != '待适配'">
                and slgh.adaptation_status like concat(  #{adaptationStatus}, '%')
            </when>
        </choose>
        <if test="adaptationStatusList != null and adaptationStatusList.size() != 0">
            and slgh.adaptation_status in
            <foreach item="adaptationStatus" collection="adaptationStatusList" open="(" separator="," close=")">
                #{adaptationStatus}
            </foreach>
        </if>
        <if test="publishStatus != null ">and slgh.publish_status like concat(#{publishStatus}, '%') </if>
        <if test="publishStatusList != null and publishStatusList.size() != 0">
            and slgh.publish_status in
            <foreach collection="publishStatusList" open="(" separator="," close=")" item="publishStatus">
                #{publishStatus}
            </foreach>
        </if>

        <if test="publishTypeList != null and publishTypeList.size() != 0">
            and slgh.publish_type in
            <foreach collection="publishTypeList" open="(" separator="," close=")" item="publishType">
                #{publishType}
            </foreach>
        </if>
        <if test="smcFlagList != null and smcFlagList.size() != 0">
            and slgh.smc_flag in
            <foreach collection="smcFlagList" open="(" separator="," close=")" item="smcFlag">
                #{smcFlag}
            </foreach>
        </if>
        <if test="brandCodeList != null and brandCodeList.size() != 0">
            and slgh.brand_code in
            <foreach collection="brandCodeList" open="(" separator="," close=")" item="brandCode">
                #{brandCode}
            </foreach>
        </if>
        <if test="(skuRateLabel != null and skuRateLabel != '') or (listingRateLabel != null and listingRateLabel != '') or (listingPerformance != null and listingPerformance != '')">
            and (
            <if test="skuRateLabel != null and skuRateLabel != ''">
                <foreach item="code" collection="skuRateLabel.split(',')" open="(" separator="or" close=")">
                    slgh.sku_rate_label = #{code}
                </foreach>
            </if>

            <if test="listingRateLabel != null and listingRateLabel != ''">
                <if test="skuRateLabel != null and skuRateLabel != ''">
                    or
                </if>
                 ( slgh.listing_rate_label in
                <foreach item="code" collection="listingRateLabel.split(',')" open="(" separator="," close=")">
                    #{code}
                </foreach>
                )
            </if>
            <if test="listingPerformance != null and listingPerformance != ''">
                <if test="skuRateLabel != null and skuRateLabel != '' or listingRateLabel != null and listingRateLabel != ''">
                    or
                </if>
                 ( sll.label in
                <foreach item="label" collection="listingPerformance.split(',')" open="(" separator="," close=")">
                    #{label}
                </foreach>)
            </if>
            )
        </if>
        <if test="pnCode != null and pnCode != ''">
            and lal.table_value=#{pnCode}
        </if>
        <if test="aplusTemplateName != null and aplusTemplateName != ''">
            and apdesc.language='en-US'  and apdesc.aplus_name like concat(#{aplusTemplateName}, '%')  and apdesc.del_flag = 0 and apasin.relation_mark=0
        </if>
        <if test="(saleValueStart != null and saleValueStart != '') or (saleValueEnd != null and saleValueEnd != '') or (orderValueStart != null and orderValueStart != '') or (orderValueEnd != null and orderValueEnd != '')">
            <choose>
                <when test="orderType != null">
                    <choose >
                        <when test="orderType==1">
                            <if test="orderValueStart != null and orderValueStart != ''">
                                and de.order_quantity_30 >=#{orderValueStart}
                            </if>
                            <if test="orderValueEnd != null and orderValueEnd != ''">
                                and de.order_quantity_30 &lt;= #{orderValueEnd}
                            </if>
                        </when>
                        <when test="orderType==2">
                            <if test="orderValueStart != null and orderValueStart != ''">
                                and de.order_quantity_60 >=#{orderValueStart}
                            </if>
                            <if test="orderValueEnd != null and orderValueEnd != ''">
                                and de.order_quantity_60 &lt;= #{orderValueEnd}
                            </if>
                        </when>
                        <when test="orderType==3">
                            <if test="orderValueStart != null and orderValueStart != ''">
                                and de.order_quantity_90 >=#{orderValueStart}
                            </if>
                            <if test="orderValueEnd != null and orderValueEnd != ''">
                                and de.order_quantity_90 &lt;= #{orderValueEnd}
                            </if>
                        </when>
                    </choose>
                </when>
            </choose>
            <choose>
                <when test="saleType != null">
                    <choose >
                        <when test="saleType==1">
                            <if test="saleValueStart != null and saleValueStart != ''">
                                and de.sales_volume_30 >=#{saleValueStart}
                            </if>
                            <if test="saleValueEnd != null and saleValueEnd != ''">
                                and de.sales_volume_30 &lt;= #{saleValueEnd}
                            </if>
                        </when>
                        <when test="saleType==2">
                            <if test="saleValueStart != null and saleValueStart != ''">
                                and de.sales_volume_60 >=#{saleValueStart}
                            </if>
                            <if test="saleValueEnd != null and saleValueEnd != ''">
                                and de.sales_volume_60 &lt;= #{saleValueEnd}
                            </if>
                        </when>
                        <when test="saleType==3">
                            <if test="saleValueStart != null and saleValueStart != ''">
                                and de.sales_volume_90 >=#{saleValueStart}
                            </if>
                            <if test="saleValueEnd != null and saleValueEnd != ''">
                                and de.sales_volume_90 &lt;= #{saleValueEnd}
                            </if>
                        </when>
                        <when test="saleType==4">
                            and de.sales_volume_30 =0 and de.sales_volume_60 =0 and de.sales_volume_90 =0
                        </when>
                        <when test="saleType==5">
                            and de.sales_volume_30 =0
                        </when>
                        <when test="saleType==6">
                            and de.sales_volume_60 =0
                        </when>
                        <when test="saleType==7">
                            and de.sales_volume_90 =0
                        </when>
                    </choose>
                </when>
            </choose>
        </if>
        <if test="redLinePriceFlag != null and redLinePriceFlag != ''">
            <if test="redLinePriceFlag == 0">
                and (case when slgh.platform = 'AM' then slgh.settlement_price &lt; slgh.red_line_price  else slgh.standard_price &lt; slgh.red_line_price end)
            </if>
            <if test="redLinePriceFlag == 1">
                and (case when slgh.platform = 'AM' then slgh.settlement_price &gt;= slgh.red_line_price  else slgh.standard_price &gt;= slgh.red_line_price end)
            </if>
                and not exists (select 1 from  temp_red_line_white_skus  wh where  wh.pdm_goods_code=slgh.pdm_goods_code )
        </if>
        <if test="isMain != null ">
            <if test="isMain == 0">
                and (slgh.is_main= 0 or slgh.is_main is null)
            </if>
            <if test="isMain == 1">
                and slgh.is_main= 1
            </if>
        </if>
        <if test="isRedLineBlack != null">
            <if test="isRedLineBlack == 0">
                and not exists (select 1 from  sc_smc_redline_price_black  red where  red.head_id=slgh.id and del_flag = 0)
            </if>
            <if test="isRedLineBlack == 1">
                and exists (select 1 from  sc_smc_redline_price_black  red where red.head_id=slgh.id and del_flag = 0)
            </if>
        </if>
        <if test="cartLabel !=null">
            <if test="cartLabel == 1 ">
                and exists (select 1 from  sc_smc_listing_label  sll where sll.head_id=slgh.id and label ='有购物车')
            </if>
            <if test="cartLabel == 0 ">
                and not exists (select 1 from  sc_smc_listing_label  sll where sll.head_id=slgh.id and label ='有购物车')
            </if>
        </if>
        <if test="isInventoryBlack != null">
            <if test="isInventoryBlack==0">
                and not exists (select 1 from sc_smc_inventory_update_black inv where inv.head_id=slgh.id and
                inv.del_flag = 0)
            </if>
            <if test="isInventoryBlack==1">
                and exists (select 1 from sc_smc_inventory_update_black inv where inv.head_id=slgh.id and inv.del_flag =
                0)
            </if>
        </if>
        <if test="isCouponBlack != null">
            <if test="isCouponBlack == 0">
                and not exists (select 1 from  sc_smc_coupon_black  coupon where  coupon.head_id=slgh.id and coupon.del_flag = 0)
            </if>
            <if test="isCouponBlack == 1">
                and exists (select 1 from  sc_smc_coupon_black  coupon where coupon.head_id=slgh.id and coupon.del_flag = 0)
            </if>
        </if>
        <if test="operationClassificationList != null and operationClassificationList.size() > 0">
            and cp.operation_classification in
            <foreach item="classification" collection="operationClassificationList" open="(" separator="," close=")">
                #{classification}
            </foreach>
        </if>
        <!-- 数据范围过滤 -->
        ${params.dataScope}
        group by slgh.id
    </select>
    <select id="countGoodsHeadVOList" resultMap="ListingGoodsHeadVOResult">
        select COUNT(DISTINCT slgh.id) counts,slgh.publish_status from
        <if test="(saleValueStart != null and saleValueStart != '') or (saleValueEnd != null and saleValueEnd != '') or (orderValueStart != null and orderValueStart != '') or (orderValueEnd != null and orderValueEnd != '')">
            sc_smc_auto_sales_detail de
            join sc_smc_listing_goods_head slgh on slgh.platform_goods_id=de.platform_sale_code
        </if>
        <if test="(saleValueStart == null or saleValueStart == '') and (saleValueEnd == null or saleValueEnd == '') and (orderValueStart == null or orderValueStart == '') and (orderValueEnd == null or orderValueEnd == '')">
            sc_smc_listing_goods_head slgh
        </if>
        <if test="pnCode != null and pnCode != ''">
            left join sc_smc_listing_amazon_attribute_line lal on lal.goods_id=slgh.id and lal.table_name='part_number'
        </if>
        <if test="aplusTemplateName != null and aplusTemplateName != ''">
            left join sc_smc_aplus_asin apasin on apasin.asin = slgh.platform_goods_id and apasin.asin != '' and apasin.asin is not null
            left join sc_smc_aplus_description apdesc on apdesc.id = apasin.aplus_id
        </if>

        <if test="listingPerformance != null and listingPerformance != ''">
            left join sc_smc_listing_label sll on sll.head_id=slgh.id
        </if>
        <if test="operationClassificationList != null and operationClassificationList.size() > 0">
            left join `suncent-pdm`.sc_cdp_cate_product cp on cp.category_code = (
            select g.product_category_code from `suncent-pdm`.sc_pdm_goods g where g.goods_code = slgh.pdm_goods_code
            limit 1
            )
        </if>

        <if test="(saleValueStart != null and saleValueStart != '') or (saleValueEnd != null and saleValueEnd != '') or (orderValueStart != null and orderValueStart != '') or (orderValueEnd != null and orderValueEnd != '')">
            where de.platform_sale_code != ''
            AND dimension = '1'
            AND de.period_id_d = #{now}
        </if>
        <if test="(saleValueStart == null or saleValueStart == '') and (saleValueEnd == null or saleValueEnd == '') and (orderValueStart == null or orderValueStart == '') and (orderValueEnd == null or orderValueEnd == '')">
            WHERE slgh.del_flag = 0
        </if>
        <if test="currentAvailability != null  and currentAvailability != ''"> AND EXISTS (
    SELECT 1 FROM sc_smc_listing_goods_head_detail sghd 
    WHERE sghd.head_id = slgh.id AND sghd.del_flag = 0 and sghd.current_availability = #{currentAvailability}
    )
</if>
        <if test="shopCodes != null  and shopCodes != ''">
            and slgh.shop_code in
            <foreach item="shopCode" collection="shopCodes" open="(" separator="," close=")">
                #{shopCode}
            </foreach>
        </if>
        <if test="createBy != null and  createBy != ''">and slgh.create_by in
            <foreach item="code" collection="createBy.split(',')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="ids != null  and ids != ''">and slgh.id in
            <foreach item="code" collection="ids.split(' ')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="productSeries != null  and productSeries != ''">and slgh.product_series = #{productSeries}</if>
            <if test="productModel != null  and productModel != ''">and slgh.product_model = #{productModel}</if>

        <if test="pdmGoodsCode != null  and pdmGoodsCode != ''  and pdmGoodsCode.contains(' '.toString() ) ">and slgh.pdm_goods_code in
            <foreach item="code" collection="pdmGoodsCode.split(' ')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="pdmGoodsCode != null  and pdmGoodsCode != '' and !pdmGoodsCode.contains(' '.toString() )">
            and slgh.pdm_goods_code like concat( #{pdmGoodsCode}, '%')
        </if>
        <!--#         <choose>-->
        <if test="platformGoodsCode != null  and platformGoodsCode != '' and platformGoodsCode.contains(' '.toString() )">
            and slgh.platform_goods_code in
            <foreach item="code" collection="platformGoodsCode.split(' ')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="platformGoodsCode != null  and platformGoodsCode != '' and !platformGoodsCode.contains(' '.toString() )">
            and slgh.platform_goods_code like concat(  #{platformGoodsCode}, '%')
        </if>
        <!--        </choose>-->
        <if test="platformGoodsId != null  and platformGoodsId != ''">and slgh.platform_goods_id in
            <foreach item="code" collection="platformGoodsId.split(' ')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="platform != null  and platform != ''">and slgh.platform = #{platform}</if>
        <if test="publishType != null ">and slgh.publish_type = #{publishType}</if>
        <if test="condition != null  and condition != ''">and slgh.`condition` = #{condition}</if>
        <if test="title != null  and title != ''">and slgh.title like concat('%', #{title}, '%')</if>
        <if test="subtitle != null  and subtitle != ''">and slgh.subtitle like concat('%', #{subtitle}, '%')</if>
        <if test="mainImageUrl != null  and mainImageUrl != ''">and slgh.main_image_url = #{mainImageUrl}</if>
        <if test="siteCode != null  and siteCode != ''">and slgh.site_code in
            <foreach item="code" collection="siteCode.split(',')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="brandCode != null  and brandCode != ''">and slgh.brand_code = #{brandCode}</if>
        <if test="shopCode != null  and shopCode != ''">and slgh.shop_code = #{shopCode}</if>
        <if test="standardPrice != null and standardPrice !='' ">and slgh.standard_price = #{standardPrice}</if>
        <if test="originalPrice != null ">and slgh.original_price = #{originalPrice}</if>
        <if test="settlementPrice != null ">and slgh.settlement_price = #{settlementPrice}</if>
        <if test="redLinePrice != null ">and slgh.red_line_price = #{redLinePrice}</if>
        <if test="settlementPrice != null ">and slgh.settlement_price = #{settlementPrice}</if>
        <if test="redLinePrice != null ">and slgh.red_line_price = #{redLinePrice}</if>
        <if test="stockOnSalesQty != null ">and slgh.stock_on_sales_qty = #{stockOnSalesQty}</if>
        <if test="delFlag != null ">and slgh.del_flag = #{delFlag}</if>
        <if test="smcFlag != null ">and slgh.smc_flag = #{smcFlag}</if>
        <if test="taskName != null and taskName != ''">and slgh.task_name = #{taskName}</if>
        <if test="categoryIds != null and  categoryIds != ''">and slgh.category_id in
            <foreach item="code" collection="categoryIds.split(',')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
            and date_format(slgh.create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
        </if>
        <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
            and date_format(slgh.create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
        </if>
        <if test="params.beginUpTime != null and params.beginUpTime != ''"><!-- 开始时间检索 -->
            and date_format(slgh.update_time,'%y%m%d') &gt;= date_format(#{params.beginUpTime},'%y%m%d')
        </if>
        <if test="params.endUpTime != null and params.endUpTime != ''"><!-- 结束时间检索 -->
            and date_format(slgh.update_time,'%y%m%d') &lt;= date_format(#{params.endUpTime},'%y%m%d')
        </if>

        <if test="params.beginOnLineTime != null and params.beginOnLineTime != ''"><!-- 上架时间检索 -->
            and date_format(slgh.online_time,'%y%m%d') &gt;= date_format(#{params.beginOnLineTime},'%y%m%d')
        </if>
        <if test="params.endOnLineTime != null and params.endOnLineTime != ''"><!-- 上架结束时间检索 -->
            and date_format(slgh.online_time,'%y%m%d') &lt;= date_format(#{params.endOnLineTime},'%y%m%d')
        </if>

        <if test="params.beginOffTime != null and params.beginOffTime != ''"><!-- 上架时间检索 -->
            and date_format(slgh.off_time,'%y%m%d') &gt;= date_format(#{params.beginOffTime},'%y%m%d')
        </if>
        <if test="params.endOffTime != null and params.endOffTime != ''"><!-- 上架结束时间检索 -->
            and date_format(slgh.off_time,'%y%m%d') &lt;= date_format(#{params.endOffTime},'%y%m%d')
        </if>
        <if test="params.publishDayStart != null"><!-- 刊登天数开始时间检索 -->
            AND DATEDIFF(CURDATE(), slgh.online_time) >= #{params.publishDayStart}
        </if>
        <if test="params.publishDayEnd != null "><!-- 刊登天数结束时间检索 -->
            AND DATEDIFF(CURDATE(), slgh.online_time) &lt;= #{params.publishDayEnd}
        </if>

        <if test="censorship != null and  censorship != ''">and slgh.censorship in
            <foreach item="code" collection="censorship.split(',')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="standardPriceStart != null and standardPriceStart != ''"><!-- 售价范围 -->
            and slgh.standard_price &gt;=  ${standardPriceStart}
        </if>
        <if test="standardPriceEnd != null and standardPriceEnd != ''"><!-- 售价范围 -->
            and slgh.standard_price &lt;=  ${standardPriceEnd}
        </if>
        <if test="stockOnSalesQtyStart != null"><!-- 库存范围 -->
            and slgh.stock_on_sales_qty &gt;= #{stockOnSalesQtyStart}
        </if>
        <if test="stockOnSalesQtyEnd != null "><!-- 库存范围 -->
            and slgh.stock_on_sales_qty &lt;= #{stockOnSalesQtyEnd}
        </if>

        <choose>
            <when test="adaptationStatus != null and adaptationStatus != '' and adaptationStatus == '待适配'">
                and (slgh.adaptation_status is null or slgh.adaptation_status ='待适配')
            </when>
            <when test="adaptationStatus != null and adaptationStatus != '' and adaptationStatus != '待适配'">
                and slgh.adaptation_status like concat(  #{adaptationStatus}, '%')
            </when>
        </choose>
        <if test="adaptationStatusList != null and adaptationStatusList.size() != 0">
            and slgh.adaptation_status in
            <foreach item="adaptationStatus" collection="adaptationStatusList" open="(" separator="," close=")">
                #{adaptationStatus}
            </foreach>
        </if>
        <if test="publishStatus != null ">and slgh.publish_status like concat(#{publishStatus}, '%') </if>

        <if test="publishTypeList != null and publishTypeList.size() != 0">
            and slgh.publish_type in
            <foreach collection="publishTypeList" open="(" separator="," close=")" item="publishType">
                #{publishType}
            </foreach>
        </if>
        <if test="smcFlagList != null and smcFlagList.size() != 0">
            and slgh.smc_flag in
            <foreach collection="smcFlagList" open="(" separator="," close=")" item="smcFlag">
                #{smcFlag}
            </foreach>
        </if>
        <if test="brandCodeList != null and brandCodeList.size() != 0">
            and slgh.brand_code in
            <foreach collection="brandCodeList" open="(" separator="," close=")" item="brandCode">
                #{brandCode}
            </foreach>
        </if>
        <if test="(skuRateLabel != null and skuRateLabel != '') or (listingRateLabel != null and listingRateLabel != '') or (listingPerformance != null and listingPerformance != '')">
            and (
            <if test="skuRateLabel != null and skuRateLabel != ''">
                <foreach item="code" collection="skuRateLabel.split(',')" open="(" separator="or" close=")">
                    slgh.sku_rate_label = #{code}
                </foreach>
            </if>

            <if test="listingRateLabel != null and listingRateLabel != ''">
                <if test="skuRateLabel != null and skuRateLabel != ''">
                    or
                </if>
                ( slgh.listing_rate_label in
                <foreach item="code" collection="listingRateLabel.split(',')" open="(" separator="," close=")">
                    #{code}
                </foreach>
                )
            </if>
            <if test="listingPerformance != null and listingPerformance != ''">
                <if test="skuRateLabel != null and skuRateLabel != '' or listingRateLabel != null and listingRateLabel != ''">
                    or
                </if>
                ( sll.label in
                <foreach item="label" collection="listingPerformance.split(',')" open="(" separator="," close=")">
                    #{label}
                </foreach>)
            </if>
            )
        </if>
        <if test="pnCode != null and pnCode != ''">
            and lal.table_value=#{pnCode}
        </if>
        <if test="isRedLineBlack != null">
            <if test="isRedLineBlack==0">
                and not exists (select 1 from  sc_smc_redline_price_black  red where  red.head_id=slgh.id and del_flag = 0)
            </if>
            <if test="isRedLineBlack==1">
                and exists (select 1 from  sc_smc_redline_price_black  red where red.head_id=slgh.id and del_flag = 0)
            </if>
        </if>
        <if test="aplusTemplateName != null and aplusTemplateName != ''">
            and apdesc.language='en-US'  and apdesc.aplus_name like concat(#{aplusTemplateName}, '%')  and apdesc.del_flag = 0 and apasin.relation_mark=0
        </if>
        <if test="(saleValueStart != null and saleValueStart != '') or (saleValueEnd != null and saleValueEnd != '') or (orderValueStart != null and orderValueStart != '') or (orderValueEnd != null and orderValueEnd != '')">
            <choose>
                <when test="orderType != null">
                    <choose >
                        <when test="orderType==1">
                            <if test="orderValueStart != null and orderValueStart != ''">
                                and de.order_quantity_30 >=#{orderValueStart}
                            </if>
                            <if test="orderValueEnd != null and orderValueEnd != ''">
                                and de.order_quantity_30 &lt;= #{orderValueEnd}
                            </if>
                        </when>
                        <when test="orderType==2">
                            <if test="orderValueStart != null and orderValueStart != ''">
                                and de.order_quantity_60 >=#{orderValueStart}
                            </if>
                            <if test="orderValueEnd != null and orderValueEnd != ''">
                                and de.order_quantity_60 &lt;= #{orderValueEnd}
                            </if>
                        </when>
                        <when test="orderType==3">
                            <if test="orderValueStart != null and orderValueStart != ''">
                                and de.order_quantity_90 >=#{orderValueStart}
                            </if>
                            <if test="orderValueEnd != null and orderValueEnd != ''">
                                and de.order_quantity_90 &lt;= #{orderValueEnd}
                            </if>
                        </when>
                    </choose>
                </when>
            </choose>
            <choose>
                <when test="saleType != null">
                    <choose >
                        <when test="saleType==1">
                            <if test="saleValueStart != null and saleValueStart != ''">
                                and de.sales_volume_30 >=#{saleValueStart}
                            </if>
                            <if test="saleValueEnd != null and saleValueEnd != ''">
                                and de.sales_volume_30 &lt;= #{saleValueEnd}
                            </if>
                        </when>
                        <when test="saleType==2">
                            <if test="saleValueStart != null and saleValueStart != ''">
                                and de.sales_volume_60 >=#{saleValueStart}
                            </if>
                            <if test="saleValueEnd != null and saleValueEnd != ''">
                                and de.sales_volume_60 &lt;= #{saleValueEnd}
                            </if>
                        </when>
                        <when test="saleType==3">
                            <if test="saleValueStart != null and saleValueStart != ''">
                                and de.sales_volume_90 >=#{saleValueStart}
                            </if>
                            <if test="saleValueEnd != null and saleValueEnd != ''">
                                and de.sales_volume_90 &lt;= #{saleValueEnd}
                            </if>
                        </when>
                        <when test="saleType==4">
                            and de.sales_volume_30 =0 and de.sales_volume_60 =0 and de.sales_volume_90 =0
                        </when>
                        <when test="saleType==5">
                            and de.sales_volume_30 =0
                        </when>
                        <when test="saleType==6">
                            and de.sales_volume_60 =0
                        </when>
                        <when test="saleType==7">
                            and de.sales_volume_90 =0
                        </when>
                    </choose>
                </when>
            </choose>
        </if>
        <if test="redLinePriceFlag != null and redLinePriceFlag != ''">
            <if test="redLinePriceFlag == 0">
                and (case when slgh.platform = 'AM' then slgh.settlement_price &lt; slgh.red_line_price  else slgh.standard_price &lt; slgh.red_line_price end)
            </if>
            <if test="redLinePriceFlag == 1">
                and (case when slgh.platform = 'AM' then slgh.settlement_price &gt;= slgh.red_line_price  else slgh.standard_price &gt;= slgh.red_line_price end)
            </if>
                and not exists (select 1 from  temp_red_line_white_skus  wh where  wh.pdm_goods_code=slgh.pdm_goods_code )
        </if>
        <if test="isMain != null ">
            <if test="isMain == 0">
                and (slgh.is_main= 0 or slgh.is_main is null)
            </if>
            <if test="isMain == 1">
                and slgh.is_main= 1
            </if>
        </if>
        <if test="cartLabel !=null">
            <if test="cartLabel == 1 ">
                and exists (select 1 from  sc_smc_listing_label  sll where sll.head_id=slgh.id and label ='有购物车')
            </if>
            <if test="cartLabel == 0 ">
                and not exists (select 1 from  sc_smc_listing_label  sll where sll.head_id=slgh.id and label ='有购物车')
            </if>
        </if>
        <if test="isInventoryBlack != null">
            <if test="isInventoryBlack==0">
                and not exists (select 1 from sc_smc_inventory_update_black inv where inv.head_id=slgh.id and
                inv.del_flag = 0)
            </if>
            <if test="isInventoryBlack==1">
                and exists (select 1 from sc_smc_inventory_update_black inv where inv.head_id=slgh.id and inv.del_flag =
                0)
            </if>
        </if>
        <if test="isCouponBlack != null">
            <if test="isCouponBlack == 0">
                and not exists (select 1 from  sc_smc_coupon_black  coupon where  coupon.head_id=slgh.id and coupon.del_flag = 0)
            </if>
            <if test="isCouponBlack == 1">
                and exists (select 1 from  sc_smc_coupon_black  coupon where coupon.head_id=slgh.id and coupon.del_flag = 0)
            </if>
        </if>
        <if test="operationClassificationList != null and operationClassificationList.size() > 0">
            and cp.operation_classification in
            <foreach item="classification" collection="operationClassificationList" open="(" separator="," close=")">
                #{classification}
            </foreach>
        </if>
        group by slgh.publish_status
    </select>
    <select id="countOnlineListing"
            resultType="com.suncent.smc.persistence.publication.domain.vo.GoodsHeadCountVO">
        SELECT platform, shop_code shopCode, pdm_goods_code goodsCode,SUM(
        CASE
        WHEN platform = 'AM' AND publish_status IN ('2', '3','3001', '4', '6', '6001', '6002', '6003') THEN 1
        WHEN platform = 'EB' AND publish_status IN ('2', '3','3001', '4') THEN 1
        ELSE 0
        END
        )  AS onLineCount
        from sc_smc_listing_goods_head
        where del_flag = 0
        and pdm_goods_code in <foreach item="code" collection="skus" open="(" separator="," close=")">
            #{code}    </foreach>
        GROUP BY platform, shop_code, pdm_goods_code
        union all
        SELECT head.platform, head.shop_code shopCode, spec.pdm_goods_code goodsCode,SUM(case when publish_status in (2,3,4,6) then 1 else 0 end) AS onLineCount
        from sc_smc_temu_listing_goods_head head
        LEFT JOIN sc_smc_temu_listing_specs spec ON head.id = spec.goods_id
        where del_flag = 0
        and spec.pdm_goods_code in <foreach item="code" collection="skus" open="(" separator="," close=")">
            #{code}    </foreach>
        GROUP BY head.platform, head.shop_code, spec.pdm_goods_code
    </select>

    <select id="selectListingByShopCode"
            resultType="com.suncent.smc.persistence.publication.domain.vo.GoodsHeadVO">
        SELECT
            h.id,
            h.platform,
            h.shop_code shopCode,
            h.pdm_goods_code pdmGoodsCode,
            h.platform_goods_code platformGoodsCode,
            h.platform_goods_id platformGoodsId,
            h.standard_price standardPrice,
            h.publish_status publishStatus,
            a.table_value listPrice
        from sc_smc_listing_goods_head h
        LEFT JOIN sc_smc_listing_amazon_attribute_line_v2 a ON h.id = a.head_id AND a.prop_node_path = 'list_price.value'
        where del_flag = 0
        <if test="shopList != null and shopList.size() != 0">
            and shop_code in
            <foreach collection="shopList" open="(" separator="," close=")" item="shopcode">
                #{shopcode}
            </foreach>
        </if>
    </select>
    <select id="selectListingCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM sc_smc_listing_goods_head
        where del_flag = 0
        <if test="goodsId != null and goodsId.size() != 0">
            and id in
            <foreach collection="goodsId" open="(" separator="," close=")" item="id">
                #{id}
            </foreach>
        </if>
    </select>
    <select id="selectAmListingByPlatformGoodsIdList" resultMap="ListingGoodsHeadResult">
        <include refid="selectListingGoodsHeadVo"/>
        where del_flag = 0 and platform = 'AM' and platform_goods_id is not null and platform_goods_id !=''
        <if test="asinList != null and asinList.size() != 0">
            and platform_goods_id in
            <foreach collection="asinList" open="(" separator="," close=")" item="id">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="countOnlineListingGroup"
            resultType="com.suncent.smc.persistence.publication.domain.vo.GoodsHeadCountVO">
        SELECT 'listing_head' tableName, shop_code shopCode, pdm_goods_code goodsCode,SUM(
        CASE
        WHEN platform = 'AM' AND publish_status IN ('2', '3','3001', '4', '6', '6001', '6002', '6003') THEN 1
        WHEN platform = 'EB' AND publish_status IN ('2', '3','3001', '4') THEN 1
        ELSE 0
        END
        )  AS onLineCount, platform
        from sc_smc_listing_goods_head
        where del_flag = 0
        and pdm_goods_code in <foreach item="code" collection="list" open="(" separator="," close=")">
        #{code}    </foreach>
        GROUP BY  shop_code, pdm_goods_code
        union all
        SELECT 'temu_listing_head', head.shop_code shopCode, spec.pdm_goods_code goodsCode,SUM(case when publish_status in (2,3,4,6) then 1 else 0 end) AS onLineCount,'TEMU'
        from sc_smc_temu_listing_goods_head head
        LEFT JOIN sc_smc_temu_listing_specs spec ON head.id = spec.goods_id
        where del_flag = 0
        and spec.pdm_goods_code in <foreach item="code" collection="list" open="(" separator="," close=")">
        #{code}    </foreach>
        GROUP BY head.shop_code, spec.pdm_goods_code
    </select>
    <select id="selectAMOnlineListingByAsinAndPlatformSku" resultMap="ListingGoodsHeadVOResult">
        <include refid="selectListingGoodsHeadVo"/>
        WHERE del_flag = 0
        AND   (platform = 'AM' AND publish_status IN ('2', '3','3001', '4', '6', '6001', '6002', '6003'))
            and platform_goods_code = #{platformSku} and platform_goods_id = #{asin} and shop_code = #{shopCode}
            <if test="siteCode != null and siteCode != ''">
                and site_code = #{siteCode}
            </if>
    </select>
    <select id="selectOnlineListingByAsinAndPlatformSku" resultMap="ListingGoodsHeadVOResult">
        <include refid="selectListingGoodsHeadVo"/>
        WHERE del_flag = 0
        AND (
        (platform = 'AM' AND publish_status IN ('2', '3','3001', '4', '6', '6001', '6002', '6003'))
        OR
        (platform = 'EB' AND publish_status IN ('2', '3','3001', '4'))
        )
        and platform_goods_code = #{platformSku} and platform_goods_id = #{asin} and shop_code = #{shopCode} and platform = #{platformCode}
        <if test="siteCode != null and siteCode != ''">
            and site_code = #{siteCode}
        </if>
    </select>
    <select id="listNeedUpdateItemsPerInnerPack" resultMap="ListingGoodsHeadResult">
        <include refid="selectListingGoodsHeadVo"/>
        WHERE del_flag = 0 and publish_type = #{publishType} and platform = 'AM' and shop_code ='VC1' and publish_status in (2, 6) and pdm_goods_code is not null and pdm_goods_code != ''
        and platform_goods_id is not null and platform_goods_id != ''
        <if test="platformSkus != null and platformSkus.size() != 0">
            and platform_goods_code in
            <foreach collection="platformSkus" open="(" separator="," close=")" item="platformSku">
                #{platformSku}
            </foreach>
        </if>
        and id &gt; #{lastId}
        order by id asc
        limit 500
    </select>
    <select id="listNeedSyncRealTimeSales" resultMap="ListingGoodsHeadResult">
        <include refid="selectListingGoodsHeadVo"/>
        WHERE del_flag = 0 and platform = 'AM' and shop_code ='VC1' and platform_goods_id is not null and platform_goods_id != ''
        <if test="platformGoodsCodes != null and platformGoodsCodes.size() != 0">
            and platform_goods_code in
            <foreach collection="platformGoodsCodes" open="(" separator="," close=")" item="platformGoodsCode">
                #{platformGoodsCode}
            </foreach>
        </if>
        order by id asc
    </select>
    <select id="selectVCDFListing" resultMap="ListingGoodsHeadVOResult">
        <include refid="selectListingGoodsHeadVo"/>
        WHERE del_flag = 0 AND shop_code = 'VC1' AND publish_type = 5
        AND platform = 'AM' AND platform_goods_id IS NOT NULL AND platform_goods_id != ''
        AND platform_goods_code IN
        <foreach item="code" collection="sellerSkuList" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>

    <select id="selectVCDFListingBySku" resultMap="ListingGoodsHeadVOResult">
        <include refid="selectListingGoodsHeadVo"/>
        WHERE del_flag = 0 AND publish_type = 5
        AND platform = 'AM' AND platform_goods_id IS NOT NULL AND platform_goods_id != ''
        AND shop_code = #{shopCode}
        AND pdm_goods_code IN
        <foreach item="code" collection="skuList" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>
    <select id="listFailListing" resultMap="ListingGoodsHeadResult">
        <include refid="selectListingGoodsHeadVo"/>
        WHERE del_flag = 0 and platform = 'AM' and shop_code ='VC1' and publish_status in (0, 8) and create_by = #{createBy}
        and create_time &gt;= DATE_SUB(NOW(), INTERVAL 7 DAY)
        order by id asc
    </select>

    <!-- 创建临时表并插入数据 用于设置主链接 -->
    <update id="createTempTableAndInsertData">
        CREATE TEMPORARY TABLE temp_id_list AS
        SELECT t1.id
        FROM sc_smc_listing_goods_head t1
                 INNER JOIN (
            SELECT
                platform_goods_id,
                MIN(online_time) AS earliest_online_time
            FROM sc_smc_listing_goods_head
            WHERE del_flag = 0
              AND platform = 'AM'
              AND platform_goods_id IS NOT NULL
              AND platform_goods_id != ''
            GROUP BY platform_goods_id
        ) t2 ON t1.platform_goods_id = t2.platform_goods_id
            AND t1.online_time = t2.earliest_online_time
        WHERE t1.del_flag = 0
          AND t1.platform = 'AM'
          AND t1.id = (
            SELECT MIN(id)
            FROM sc_smc_listing_goods_head t3
            WHERE t3.platform_goods_id = t1.platform_goods_id
              AND t3.online_time = t1.online_time
              AND t3.del_flag = 0
              AND t3.platform = 'AM'
        );
    </update>

    <!-- 更新AM主表主链接为空 -->
    <update id="clearAMainTable">
        UPDATE
            sc_smc_listing_goods_head
        SET
            is_main = NULL
        WHERE
            del_flag = 0
          AND platform = 'AM'
          AND platform_goods_id IS NOT NULL
          AND platform_goods_id != ''

    </update>

    <!-- 更新主表 -->
    <update id="updateMainTable">
        UPDATE sc_smc_listing_goods_head h
            JOIN temp_id_list t ON h.id = t.id
            SET h.is_main = 1;
    </update>

    <!-- 删除临时表 -->
    <update id="dropTempTable">
        DROP TEMPORARY TABLE IF EXISTS temp_id_list;
    </update>

    <update id="updateZeroInventory">
        UPDATE sc_smc_listing_goods_head SET stock_on_sales_qty = 0
        where del_flag = 0 and id in
        <foreach item="id" collection="goodsId" open="(" separator="," close=")">
        #{id}
        </foreach>
    </update>


    <update id="updateHeadsAdapterByItemId">
        <foreach collection="goodsHeads" item="record" separator=";">
            UPDATE sc_smc_listing_goods_head
            SET
            adaptation_status = #{record.adaptationStatus}
            WHERE
            platform_goods_id = #{record.platformGoodsId}
        </foreach>
    </update>


    <select id="listFollowSoldAsin" resultType="string">
        SELECT platform_goods_id FROM  sc_smc_listing_goods_head
            WHERE del_flag = 0
            AND platform = 'AM'
            AND publish_type = #{publishType}
            AND platform_goods_id IN (
                SELECT platform_goods_id FROM  sc_smc_listing_goods_head
                WHERE id IN
                <foreach item="id" collection="goodsId" open="(" separator="," close=")">
                    #{id}
                </foreach>
            )
    </select>

    <update id="updateVcInventoryBySellerSku">
        UPDATE sc_smc_listing_goods_head lg
        JOIN (
        SELECT
        inv.seller_sku,
        SUM( inv.available_inventory ) AS total_units
        FROM
        sc_smc_vc_listing_inventory inv
        WHERE
        inv.warehouse_code IN ( SELECT DISTINCT ( wm.am_wh_code ) FROM sc_smc_amazon_warehouse_mapping wm WHERE
        wm.del_flag = 0 )
        AND inv.seller_sku IN
        <foreach item="id" collection="sellerSkuLit" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY
        inv.seller_sku
        ) stats ON lg.platform_goods_code = stats.seller_sku
        SET lg.stock_on_sales_qty = stats.total_units
        WHERE lg.shop_code LIKE 'VC%' AND lg.publish_type = '5';
    </update>

    <update id="updateListingOffSale">
        update sc_smc_listing_goods_head
        set publish_status = #{publishStatus}
        where del_flag = 0
          and shop_code = #{accountCode}
          and platform_goods_id = #{itemId}
    </update>


    <select id="selectDeleteListingGoodsHeadList"  resultMap="ListingGoodsHeadResult">
        select id,
               pdm_goods_code,
               platform_goods_code,
               platform_goods_id,
               platform,
               shop_code,
               publish_type
            from sc_smc_listing_goods_head
        WHERE del_flag = 2  and platform_goods_id =#{asin} and shop_code=#{shopCode} and platform_goods_code =#{sellerSku} and publish_type = #{publishType}
        and create_time &gt;= DATE_SUB(NOW(), INTERVAL ${day} DAY)
    </select>
    <select id="listWaitAdaptByLastId" resultMap="ListingGoodsHeadResult">
        <include refid="selectListingGoodsHeadVo"/>
        WHERE del_flag = 0
        AND platform = 'AM'  and platform_goods_id is not null and platform_goods_id != '' and platform_goods_code is not null and platform_goods_code != ''
        AND (adaptation_status = '待适配' or adaptation_status is null)
        and id &gt; #{lastId}
        order by id asc
        limit 500
    </select>
    <select id="listNeedSyncSeriesByLastId" resultMap="ListingGoodsHeadResult">
        <include refid="selectListingGoodsHeadVo"/>
        WHERE del_flag = 0 and id &gt; #{lastId} and pdm_goods_code is not null and pdm_goods_code != ''
        order by id asc
        limit 1000
    </select>
    <select id="selectLastGoodsHeadByAsin" resultMap="ListingGoodsHeadResult">
        <include refid="selectListingGoodsHeadVo"/>
        WHERE del_flag = 0 and platform = 'AM' and platform_goods_id = #{asin} and id != #{id} and adaptation_status is not null
        order by id desc
        limit 1
    </select>
    <select id="listNeedUpdatePublishStatus" resultMap="ListingGoodsHeadResult">
        <include refid="selectListingGoodsHeadVo"/>
        WHERE del_flag = 0 and platform = 'AM' and shop_code ='VC1' and platform_goods_id is not null and platform_goods_id != ''
        and id &gt; #{lastId}
        order by id asc
        limit 1000
    </select>

    <select id="listNeedUpdatePublishStatusPaged" resultMap="ListingGoodsHeadResult">
        <include refid="selectListingGoodsHeadVo"/>
        WHERE del_flag = 0 and platform = 'AM' and shop_code ='VC1' and platform_goods_id is not null and
        platform_goods_id != ''
        order by id asc
        limit #{limit} offset #{offset}
    </select>

    <select id="countByPlatformGoodsCodeAndShopCodeAndPublishType" resultType="java.lang.Integer">
        SELECT count(1)
        FROM sc_smc_listing_goods_head
        WHERE del_flag = 0
        AND platform_goods_code = #{platformSku} and id != #{id}
        AND shop_code = #{shopCode}
        AND publish_type = #{publishType} and platform = 'AM'
    </select>
    <select id="selectAMList" resultMap="ListingGoodsHeadResult">
        <include refid="selectListingGoodsHeadVo"/>
        WHERE del_flag = 0 and platform = 'AM' and platform_goods_id is not null and platform_goods_id != ''
        and create_by = #{createBy} and shop_code = #{shopCode} and category_id = #{categoryId}
    </select>
    <select id="selectGoodsHeadByShopCodeAndAsin" resultMap="ListingGoodsHeadResult">
        <include refid="selectListingGoodsHeadVo2"/>
        where del_flag=0 and platform='AM'
        and platform_goods_code is not null and platform_goods_code !='' and shop_code = #{shopCode} and platform_goods_id in
        <foreach item="asin" collection="asinList" open="(" separator="," close=")">
            #{asin}
        </foreach>
    </select>
    <select id="selectScAsinList" resultMap="ListingGoodsHeadResult">
        <include refid="selectListingGoodsHeadVo2"/>
        WHERE del_flag = 0 and platform = 'AM' and platform_goods_id is not null and platform_goods_id != '' and online_time is not null and online_time !=''
        and id &gt;=#{minId} and id &lt;=#{maxId} and online_time &lt;= DATE_SUB(NOW(), INTERVAL 270 DAY)
        order by id asc
        limit 1000
    </select>
    <select id="selectMaxIdBefore270Days" resultType="java.lang.Integer">
        select max(id) from sc_smc_listing_goods_head
        where del_flag = 0 and platform = 'AM' and platform_goods_id is not null and platform_goods_id != ''
        and online_time &lt;= DATE_SUB(NOW(), INTERVAL 270 DAY)
    </select>
    <select id="listTempAsinList" resultType="java.lang.String">
        select asin
        from temp_asin
        order by id asc
        limit 100
    </select>
    <select id="selectGoodsHeadByAsin" resultMap="ListingGoodsHeadResult">
        <include refid="selectListingGoodsHeadVo2"/>
        where del_flag=0
        and platform_goods_id in
        <foreach item="asin" collection="asinList" open="(" separator="," close=")">
            #{asin}
        </foreach>
        <if test="platform != null  and platform != ''">and platform = #{platform}</if>
    </select>
    <select id="getNeedBackupStartId" resultType="java.lang.Long">
        select id from sc_smc_listing_goods_head where del_flag = 2 and update_time &lt;= DATE_SUB(NOW(), INTERVAL 1 YEAR) order by id asc limit 1
    </select>
    <select id="listNeedBackup"  resultMap="ListingGoodsHeadResult">
        <include refid="selectListingGoodsHeadVo"/>
        where del_flag = 2 and id &gt;= #{lastId} and id &lt;= #{maxId} and update_time &lt;= DATE_SUB(NOW(), INTERVAL 1 YEAR)
        order by id asc
    </select>
    <select id="listMainBrandListing"
            resultType="com.suncent.smc.persistence.publication.domain.vo.GoodsHeadCountVO">
        select pdm_goods_code goodsCode, count(1) as mainBrandCount
        from sc_smc_listing_goods_head
        where del_flag = 0 and platform = 'AM'  and platform_goods_id is not null and platform_goods_id != ''
        and pdm_goods_code in

        <foreach item="goodsCode" collection="goodsCodeList" open="(" separator="," close=")">
            #{goodsCode}
        </foreach> and brand_code in
        <foreach item="mainBrand" collection="mainBrandList" open="(" separator="," close=")">
            #{mainBrand}
        </foreach>
        group by pdm_goods_code
    </select>
    <select id="selectAMListingByBrand" resultMap="ListingGoodsHeadResult">
        select  id,platform_goods_id from sc_smc_listing_goods_head
        WHERE del_flag = 0 and platform = 'AM' and platform_goods_id is not null and platform_goods_id != ''
        and brand_code = #{brandCode} and id &gt; #{lastId} and online_time &lt;= #{beforeDate}
        order by id asc
        limit 1000
    </select>

    <select id="selectGoodsHeadByShopCodeAndPlatformGoodsCode" resultMap="ListingGoodsHeadResult">
        <include refid="selectListingGoodsHeadVo2"/>
        where del_flag=0 and platform='AM' and shop_code = #{shopCode} and publish_type = #{publishType} and platform_goods_code in
        <foreach item="platformSku" collection="platformSkus" open="(" separator="," close=")">
            #{platformSku}
        </foreach>
    </select>

    <select id="listVcErrorData" parameterType="map"
            resultType="com.suncent.smc.persistence.publication.domain.entity.VcLinkErrorDataVO">
        SELECT DISTINCT lg.id,lg.pdm_goods_code pdmGoodsCode,lg.platform_goods_code platformGoodsCode,lg.platform_goods_id platformGoodsId,lg.shop_code shopCode,
               u.user_name operator,lg.online_time onlineTime,
               lg.create_time createTime,pc.category_detail categoryName,pc.category_en_detail categoryNameEn,sl2.label, sl2.label_type labelType,case when lg.publish_type = 5 then 'VCDF' when lg.publish_type=6 then 'VCPO' END publishType
        FROM sc_smc_listing_goods_head lg
        INNER JOIN (
            SELECT DISTINCT head_id
            FROM sc_smc_listing_label
            WHERE (
                <choose>
                    <when test="period == 'day'">
                        label = 'AndonCord'
                    </when>
                    <when test="period == 'week'">
                        label IN ('变狗', 'HighCost', 'LP异常') OR label_type = 'api-netppm'
                    </when>
                    <otherwise>
                        label IN ('变狗', 'HighCost', 'LP异常', 'BD异常', 'Coupon异常', 'AndonCord') OR label_type = 'api-netppm'
                    </otherwise>
                </choose>
            )
        ) target_heads ON lg.id = target_heads.head_id
        LEFT JOIN sc_smc_listing_label sl2 ON lg.id = sl2.head_id
            AND (sl2.label IN ('变狗', 'HighCost', 'LP异常', 'BD异常', 'Coupon异常', 'AndonCord') OR sl2.label_type = 'api-netppm')
        LEFT JOIN sc_smc_platform_category pc ON lg.category_id = pc.id
        LEFT JOIN sys_user u ON lg.create_by = u.user_id
        WHERE lg.del_flag = 0
        AND lg.shop_code = 'VC1'
        AND lg.platform = 'AM'
        AND lg.platform_goods_id IS NOT NULL
        AND lg.platform_goods_id != ''
        AND lg.create_by = #{userId}
        AND sl2.label IS NOT NULL
    </select>

    <select id="queryNeedPullEbayListings" resultMap="ListingGoodsHeadResult">
        SELECT id, shop_code, platform_goods_id, update_time FROM `suncent-smc`.`sc_smc_listing_goods_head`
        WHERE `del_flag` = '0' AND `platform` = 'EB' AND publish_status IN ( '2', '4')
            AND ( `update_time` &lt; CURDATE() - INTERVAL 7 DAY OR ( (standard_price / red_line_price) &lt; 0.6  AND `update_time` &lt; CURDATE() - INTERVAL 3 DAY ) )
        ORDER BY update_time
        limit 2000
    </select>


    <select id="queryNeedPullAmazonListings" resultMap="ListingGoodsHeadResult">
        SELECT  head.platform_goods_code,head.platform_goods_id,head.shop_code
        FROM `suncent-smc`.`sc_smc_listing_goods_head` head
                LEFT JOIN  `suncent-pdm`.`sc_pdm_mapping_goods` mapping
                           on head.platform_goods_code =mapping.platform_sku and head.shop_code=mapping.shop_code
        WHERE  head.`del_flag` = '0' AND head.`platform` = 'AM' AND (head.pdm_goods_code is null or head.pdm_goods_code ='' ) and mapping.id !='' AND mapping.del_flag='0'
        limit 2000
    </select>
    <select id="queryMonitorPoolVCPrice"
            resultType="com.suncent.smc.persistence.publication.domain.vo.VCPriceVO">
        SELECT
        h.platform_goods_id as asin,
        h.pdm_goods_code as sku,
        h.standard_price as costPrice,
        a.table_value as listPrice,
        h.publish_type publishType
        FROM   sc_smc_listing_goods_head h
        LEFT JOIN sc_smc_listing_amazon_attribute_line_v2 a ON h.id = a.head_id
        AND a.prop_node_path = 'list_price.value'
        where h.platform='AM' and h.shop_code='VC1' and del_flag=0 and (h.platform_goods_id, h.pdm_goods_code) IN ( <foreach collection="vcPriceDTOS" item="item" separator=",">
        (#{item.asin}, #{item.sku})
    </foreach>)
    </select>
    <select id="listByPlatformAndShopCode"  resultMap="ListingGoodsHeadResult">
        select lgh.id,
        lgh.pdm_goods_code,
        lgh.platform_goods_code,
        lgh.platform_goods_id,
        lgh.platform,
        lgh.shop_code,
        lgh.publish_type,
        lgh.`condition`,
        lgh.title,
        lgh.subtitle,
        lgh.adaptation_status,
        lgh.publish_status,
        lgh.main_image_url,
        lgh.site_code,
        lgh.brand_code,
        lgh.standard_price,
        lgh.original_price,
        lgh.settlement_price,
        lgh.red_line_price,
        lgh.stock_on_sales_qty,
        lgh.create_by,
        lgh.create_time,
        lgh.update_by,
        lgh.update_time,
        lgh.del_flag,
        lgh.category_id,
        lgh.publishing_handler,
        lgh.online_time,
        lgh.off_time,
        lgh.task_name,
        lgh.smc_flag,
        lgh.censorship,
        lgh.pdm_status,
        lgh.listing_rate_label,
        lgh.sku_rate_label,
        lgh.is_main,
        lgh.product_series,
        lgh.product_model,
        lpd.update_time as pullDate
        from sc_smc_listing_goods_head lgh
        left join sc_smc_listing_pull_date lpd on lgh.id=lpd.head_id
        where lgh.del_flag=0 and lgh.platform=#{platform} and lgh.shop_code = #{shopCode} and platform_goods_id is not null and platform_goods_id != ''
        and platform_goods_code in
        <foreach item="platformSku" collection="platformSkuList" open="(" separator="," close=")">
            #{platformSku}
        </foreach>
    </select>
    <select id="selectBrandByAsin" resultType="java.lang.String">
        SELECT brand_code 
        FROM sc_smc_listing_goods_head 
        WHERE platform_goods_id = #{asin} 
        AND del_flag = 0 
        LIMIT 1
    </select>

    <update id="truncateTempRedLineWhiteSkus">
        TRUNCATE TABLE temp_red_line_white_skus
    </update>
    <update id="clearTempAsinSalesTable">
        TRUNCATE TABLE temp_asin_sales
    </update>

    <insert id="insertTempRedLineWhiteSkus">
        INSERT INTO temp_red_line_white_skus (pdm_goods_code, create_time) VALUES
        <foreach collection="pdmGoodsCodes" item="item" separator=",">
            ( #{item}, NOW() )
        </foreach>
    </insert>
    <insert id="insertAsinSales">
        INSERT IGNORE INTO temp_asin_sales (asin) VALUES
        <foreach collection="asinList" item="item" separator=",">
            ( #{item})
        </foreach>
    </insert>


    <!-- 根据ASIN查询其他链接 -->
    <select id="selectOtherListingsByAsin" resultMap="ListingGoodsHeadResult">
        SELECT
            id,
            publish_status,
            platform_goods_id,
            platform_goods_code,
            title,
            shop_code,
            pdm_goods_code,
            site_code,
            platform,
            category_id,
            publish_type
        FROM
            sc_smc_listing_goods_head
        WHERE
            platform_goods_id = #{asin}
            AND del_flag = 0
            <if test="currentHeadId != null">
                AND id != #{currentHeadId}
            </if>
        ORDER BY
            update_time DESC
    </select>
    <select id="listAsinSales" resultType="java.lang.String">
        SELECT asin FROM temp_asin_sales order by id asc LIMIT #{batchSize}
    </select>

    <select id="selectListingGoodsHeadByAsinAndShopCode" resultType="java.lang.String">
        select id from sc_smc_listing_goods_head
        where del_flag=0
        <if test="asin !=null">
            and platform_goods_id=#{asin}
        </if>
        <if test="shopCode !=null">
            and shop_code= #{shopCode}
        </if>
        <if test="sku !=null">
            and platform_goods_code= #{sku}
        </if>
        <if test="publishType !=null">
            and publish_type= #{publishType}
        </if>
        limit 1
    </select>
    <select id="listVCAvailableAsins" resultMap="ListingGoodsHeadResult">
        select id,
        pdm_goods_code,
        platform_goods_code,
        platform_goods_id,
        platform,
        shop_code,
        publish_type,
        `condition`,
        title,
        subtitle,
        adaptation_status,
        publish_status,
        main_image_url,
        site_code,
        brand_code,
        standard_price,
        original_price,
        settlement_price,
        red_line_price,
        stock_on_sales_qty,
        create_by,
        create_time,
        update_by,
        update_time,
        remark,
        del_flag,
        category_id,
        publishing_handler,
        online_time,
        off_time,
        task_name,
        smc_flag,
        censorship,
        pdm_status,
        listing_rate_label,
        sku_rate_label,
        is_main,product_series,product_model,
        (SELECT table_value FROM sc_smc_listing_amazon_attribute_line_v2 a WHERE a.head_id = id AND a.prop_node_path = 'list_price.value' limit 1) as listPrice
        from sc_smc_listing_goods_head
        WHERE del_flag=0 and platform='AM' and shop_code='VC1' and platform_goods_id is not null and platform_goods_id != ''
        and publish_type = #{publishType}
        <if test="ids != null  and ids != ''">and id in
            <foreach item="code" collection="ids.split(' ')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>

        <if test="pdmGoodsCode != null  and pdmGoodsCode != ''  and pdmGoodsCode.contains(' '.toString() ) ">and pdm_goods_code in
            <foreach item="code" collection="pdmGoodsCode.split(' ')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="pdmGoodsCode != null  and pdmGoodsCode != '' and !pdmGoodsCode.contains(' '.toString() )">
            and pdm_goods_code like concat( #{pdmGoodsCode}, '%')
        </if>

        <if test="platformGoodsCode != null  and platformGoodsCode != '' and platformGoodsCode.contains(' '.toString() )">
            and platform_goods_code in
            <foreach item="code" collection="platformGoodsCode.split(' ')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="platformGoodsCode != null  and platformGoodsCode != '' and !platformGoodsCode.contains(' '.toString() )">
            and platform_goods_code like concat(  #{platformGoodsCode}, '%')
        </if>

        <if test="platformGoodsId != null  and platformGoodsId != ''">and platform_goods_id in
            <foreach item="code" collection="platformGoodsId.split(' ')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>

        <if test="pnCode != null and pnCode != ''">
            and EXISTS (
                SELECT 1 FROM sc_smc_listing_amazon_attribute_line lal
                WHERE lal.goods_id = id AND lal.table_name = 'part_number' AND lal.table_value = #{pnCode}
            )
        </if>
    </select>

    <delete id="deleteAsinSales">
        DELETE FROM temp_asin_sales
        WHERE asin IN
        <foreach collection="asinList" open="(" separator="," close=")" item="asin">
            #{asin}
        </foreach>
    </delete>

    <!-- 根据ASIN批量查询head_id和pdm_goods_code -->
    <select id="selectHeadIdAndPdmCodeByAsinList" resultMap="ListingGoodsHeadResult">
        SELECT
        id,
        platform_goods_id,
        pdm_goods_code
        FROM sc_smc_listing_goods_head
        WHERE del_flag = 0
        AND platform = 'AM'
        AND platform_goods_id IS NOT NULL
        AND platform_goods_id != ''
        <if test="asinList != null and asinList.size() > 0">
            AND platform_goods_id IN
            <foreach collection="asinList" open="(" separator="," close=")" item="asin">
                #{asin}
            </foreach>
        </if>
    </select>
</mapper>