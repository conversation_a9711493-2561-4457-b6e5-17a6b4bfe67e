package com.suncent.smc.persistence.promotion.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.suncent.smc.common.core.text.Convert;
import com.suncent.smc.common.utils.DateUtils;
import com.suncent.smc.common.utils.ShiroUtils;
import com.suncent.smc.persistence.promotion.domain.dto.PromotionAsinEvent;
import com.suncent.smc.persistence.promotion.domain.dto.PromotionEvent;
import com.suncent.smc.persistence.promotion.domain.entity.AmBestDealAsin;
import com.suncent.smc.persistence.promotion.domain.entity.AmBestDealRecord;
import com.suncent.smc.persistence.promotion.mapper.AmBestDealAsinMapper;
import com.suncent.smc.persistence.promotion.mapper.AmBestDealRecordMapper;
import com.suncent.smc.persistence.promotion.service.IAmBestDealRecordService;
import com.suncent.smc.persistence.promotion.service.IAmPromotionLogService;
import com.suncent.smc.persistence.publication.domain.entity.GoodsHead;
import com.suncent.smc.persistence.publication.service.IGoodsHeadService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * BD促销记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Service
@Slf4j
public class AmBestDealRecordServiceImpl implements IAmBestDealRecordService {
    @Autowired
    private AmBestDealRecordMapper amBestDealRecordMapper;

    @Autowired
    private AmBestDealAsinMapper amBestDealAsinMapper;

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    @Autowired
    private IAmPromotionLogService promotionLogService;
    @Autowired
    private IGoodsHeadService goodsHeadService;
    /**
     * 查询BD促销记录
     *
     * @param id BD促销记录主键
     * @return BD促销记录
     */
    @Override
    public AmBestDealRecord selectAmBestDealRecordById(Long id) {
        AmBestDealRecord record = amBestDealRecordMapper.selectAmBestDealRecordById(id);
        if (record != null) {
            // 加载关联的ASIN列表
            List<AmBestDealAsin> asinList = amBestDealAsinMapper.selectAmBestDealAsinByRefId(id);
            record.setAsinList(asinList);
        }
        return record;
    }

    /**
     * 查询BD促销记录列表
     *
     * @param amBestDealRecord BD促销记录
     * @return BD促销记录
     */
    @Override
    public List<AmBestDealRecord> selectAmBestDealRecordList(AmBestDealRecord amBestDealRecord) {
        return amBestDealRecordMapper.selectAmBestDealRecordList(amBestDealRecord);
    }


    /**
     * 查询BD促销记录列表（包含ASIN数量和ASIN列表数据）
     *
     * @param amBestDealRecord BD促销记录
     * @return BD促销记录
     */
    @Override
    public List<AmBestDealRecord> selectAmBestDealRecordListWithAsinData(AmBestDealRecord amBestDealRecord) {
        List<AmBestDealRecord> records = amBestDealRecordMapper.selectAmBestDealRecordListWithAsinCount(amBestDealRecord);

        if (CollUtil.isEmpty(records)) {
            return records;
        }

        // 提取所有记录ID
        List<Long> recordIds = records.stream()
                .filter(record -> record.getId() != null)
                .map(AmBestDealRecord::getId)
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(recordIds)) {
            return records;
        }

        // 批量查询所有ASIN数据
        List<AmBestDealAsin> allAsins = amBestDealAsinMapper.selectAmBestDealAsinByRefIds(recordIds);

        // 按refBestDealId分组
        Map<Long, List<AmBestDealAsin>> asinMap = allAsins.stream()
                .collect(Collectors.groupingBy(AmBestDealAsin::getRefBestDealId));

        // 为每个记录设置对应的ASIN列表
        for (AmBestDealRecord record : records) {
            if (record.getId() != null) {
                List<AmBestDealAsin> asinList = asinMap.getOrDefault(record.getId(), new ArrayList<>());
                record.setAsinList(asinList);
            }
        }

        return records;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveAmBestDealRecord(AmBestDealRecord amBestDealRecord, Map<Integer, GoodsHead> goodsHeadMap) {
        amBestDealRecord.setCreateTime(DateUtils.getNowDate());
        int result = amBestDealRecordMapper.insertAmBestDealRecord(amBestDealRecord);

        // 插入关联的ASIN记录
        if (result > 0 && CollUtil.isNotEmpty(amBestDealRecord.getAsinList())) {
            for (AmBestDealAsin asin : amBestDealRecord.getAsinList()) {
                GoodsHead goodsHead = goodsHeadMap.get(asin.getHeadId().intValue());
                if (goodsHead != null) {
                    asin.setPlatformGoodsId(goodsHead.getPlatformGoodsId());
                    asin.setPlatformGoodsCode(goodsHead.getPlatformGoodsCode());
                    asin.setPdmGoodsCode(goodsHead.getPdmGoodsCode());
                }
                asin.setRefBestDealId(amBestDealRecord.getId());
                asin.setCreateTime(DateUtils.getNowDate());
                asin.setCreateBy(amBestDealRecord.getCreateBy());
                amBestDealAsinMapper.insertAmBestDealAsin(asin);

                // 发布ASIN添加事件
                PromotionAsinEvent asinEvent = PromotionAsinEvent.asinAddEvent(
                        this, amBestDealRecord, asin, getCurrentUserId());
                eventPublisher.publishEvent(asinEvent);
            }
        }

        // 发布BD创建事件和记录日志
        if (result > 0) {
            PromotionEvent event = PromotionEvent.createEvent(this, amBestDealRecord, getCurrentUserId());
            eventPublisher.publishEvent(event);

            // 记录操作日志
            recordPromotionLog(amBestDealRecord.getId().intValue(), 1, "创建BD促销",
                    String.format("创建BD促销记录：%s，包含%d个ASIN",
                            amBestDealRecord.getPromotionName(),
                            CollUtil.isEmpty(amBestDealRecord.getAsinList()) ? 0 : amBestDealRecord.getAsinList().size()), 0, null);
        }

        return result;
    }

    /**
     * 新增BD促销记录
     *
     * @param amBestDealRecord BD促销记录
     * @return 结果
     */
    @Override
    @Transactional
    public int insertAmBestDealRecord(AmBestDealRecord amBestDealRecord) {
        amBestDealRecord.setCreateTime(DateUtils.getNowDate());
        int result = amBestDealRecordMapper.insertAmBestDealRecord(amBestDealRecord);
        return result;
    }

    /**
     * 修改BD促销记录
     *
     * @param amBestDealRecord BD促销记录
     * @return 结果
     */
    @Override
    @Transactional
    public int updateAmBestDealRecord(AmBestDealRecord amBestDealRecord) {
        // 获取更新前的数据用于变更对比
        AmBestDealRecord oldRecord = selectAmBestDealRecordById(amBestDealRecord.getId());
        if (oldRecord == null) {
            throw new RuntimeException("BD促销记录不存在，ID: " + amBestDealRecord.getId());
        }

        // 记录变更前的数据
        Map<String, Object> beforeData = convertRecordToMap(oldRecord);
        List<AmBestDealAsin> oldAsinList = oldRecord.getAsinList();

        // 更新主记录
        amBestDealRecord.setUpdateTime(DateUtils.getNowDate());
        int result = amBestDealRecordMapper.updateAmBestDealRecord(amBestDealRecord);

        // 处理ASIN关联关系的变更
        if (result > 0) {
            List<AmBestDealAsin> newAsinList = amBestDealRecord.getAsinList();
            processAsinChanges(amBestDealRecord.getId(), oldAsinList, newAsinList);

            // 记录变更后的数据
            Map<String, Object> afterData = convertRecordToMap(amBestDealRecord);

            // 发布UPDATE事件和记录日志
            publishUpdateEvent(oldRecord, amBestDealRecord, beforeData, afterData);

            // 记录操作日志
            recordPromotionLog(amBestDealRecord.getId().intValue(), 1, "更新BD促销",
                    String.format("更新BD促销记录：%s", amBestDealRecord.getPromotionName()), 0, null);
        }

        return result;
    }

    /**
     * 批量删除BD促销记录
     *
     * @param ids 需要删除的BD促销记录主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteAmBestDealRecordByIds(String ids) {
        String[] idArray = Convert.toStrArray(ids);
        if (idArray == null || idArray.length == 0) {
            return 0;
        }

        // 批量获取要删除的记录信息（用于状态验证和事件发布）
        List<Long> idList = Arrays.stream(idArray)
            .map(Long::valueOf)
            .collect(Collectors.toList());

        Map<Long, AmBestDealRecord> recordMap = batchGetRecordsByIds(idList);

        // 1. 验证所有记录都是草稿状态
        for (Map.Entry<Long, AmBestDealRecord> entry : recordMap.entrySet()) {
            AmBestDealRecord record = entry.getValue();
            if (record != null && !"DRAFT".equals(record.getStatus())) {
                throw new RuntimeException(String.format(
                    "只能删除草稿状态的促销记录，记录ID=%d，当前状态=%s",
                    entry.getKey(), record.getStatus()));
            }
        }

        // 2. 批量删除关联的ASIN记录并发布ASIN删除事件
        batchDeleteRelatedAsins(recordMap);

        // 3. 执行主记录删除
        int result = amBestDealRecordMapper.deleteAmBestDealRecordByIds(idArray);

        // 4. 批量发布主记录删除事件
        if (result > 0) {
            batchPublishDeleteEvents(recordMap);
        }

        return result;
    }

    /**
     * 删除BD促销记录信息
     *
     * @param id BD促销记录主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteAmBestDealRecordById(Long id) {
        // 获取要删除的记录信息
        AmBestDealRecord record = selectAmBestDealRecordById(id);
        if (record == null) {
            return 0;
        }

        // 验证只能删除草稿状态的记录
        if (!"DRAFT".equals(record.getStatus())) {
            throw new RuntimeException("只能删除草稿状态的促销记录，当前状态：" + record.getStatus());
        }

        // 1. 先删除关联的ASIN记录并记录ASIN删除事件
        List<AmBestDealAsin> asinList = record.getAsinList();
        if (CollUtil.isNotEmpty(asinList)) {
            for (AmBestDealAsin asin : asinList) {
                // 发布ASIN删除事件
                PromotionAsinEvent asinDeleteEvent = PromotionAsinEvent.asinRemoveEvent(
                    this, record, asin, getCurrentUserId());
                eventPublisher.publishEvent(asinDeleteEvent);
            }

            // 删除ASIN关联数据
            amBestDealAsinMapper.deleteAmBestDealAsinByRefId(id);
        }

        // 2. 删除主记录
        int result = amBestDealRecordMapper.deleteAmBestDealRecordById(id);

        // 3. 发布主记录删除事件和记录日志
        if (result > 0) {
            PromotionEvent event = PromotionEvent.deleteEvent(this, record, getCurrentUserId());
            eventPublisher.publishEvent(event);

            // 记录操作日志
            recordPromotionLog(id.intValue(), 1, "删除BD促销",
                    String.format("删除BD促销记录：%s，包含%d个ASIN",
                        record.getPromotionName(), asinList != null ? asinList.size() : 0), 0, null);
        }

        return result;
    }

    /**
     * 批量新增BD促销记录
     * 使用事务保证全部成功或全部失败，不使用try-catch处理单个记录异常
     *
     * @param records BD促销记录列表
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchInsertAmBestDealRecord(List<AmBestDealRecord> records) {
        List<Integer> headIds = records.stream().map(AmBestDealRecord::getAsinList).filter(Objects::nonNull).map(
            asinList -> asinList.stream().map(e -> e.getHeadId().intValue()).distinct().collect(Collectors.toList())
        ).flatMap(List::stream).collect(Collectors.toList());
        Map<Integer, GoodsHead> goodsHeadMap = new HashMap<>();
        if (CollUtil.isNotEmpty(headIds)) {
            List<GoodsHead> goodsHeads = goodsHeadService.selectListingGoodsHeadByIds(headIds.toArray(new Integer[0]));
            goodsHeadMap = goodsHeads.stream().collect(Collectors.toMap(GoodsHead::getId, Function.identity()));
        }

        int totalResult = 0;
        for (AmBestDealRecord record : records) {
            int result = saveAmBestDealRecord(record, goodsHeadMap);
            totalResult += result;

            // 如果插入失败，直接抛出异常，触发事务回滚
            if (result <= 0) {
                throw new RuntimeException("BD促销记录插入失败：" + record.getPromotionName());
            }
        }

        return totalResult;
    }

    /**
     * 根据促销ID查询BD记录
     *
     * @param promotionId 促销ID
     * @return BD促销记录
     */
    @Override
    public AmBestDealRecord selectAmBestDealRecordByPromotionId(String promotionId) {
        return amBestDealRecordMapper.selectAmBestDealRecordByPromotionId(promotionId);
    }

    /**
     * 根据促销ID列表批量查询BD记录
     *
     * @param promotionIds 促销ID列表
     * @return BD促销记录集合
     */
    @Override
    public List<AmBestDealRecord> selectAmBestDealRecordByPromotionIds(List<String> promotionIds) {
        if (CollUtil.isEmpty(promotionIds)) {
            return new ArrayList<>();
        }
        return amBestDealRecordMapper.selectAmBestDealRecordByPromotionIds(promotionIds);
    }

    /**
     * 根据站点和状态查询BD记录列表
     * 
     * @param site 站点
     * @param status 状态
     * @return BD促销记录集合
     */
    @Override
    public List<AmBestDealRecord> selectAmBestDealRecordBySiteAndStatus(String site, String status) {
        return amBestDealRecordMapper.selectAmBestDealRecordBySiteAndStatus(site, status);
    }

    /**
     * 更新BD促销记录状态
     *
     * @param id BD记录ID
     * @param newStatus 新状态
     * @return 结果
     */
    @Override
    @Transactional
    public int updateAmBestDealRecordStatus(Long id, String newStatus) {
        AmBestDealRecord record = selectAmBestDealRecordById(id);
        if (record == null) {
            throw new RuntimeException("BD促销记录不存在，ID: " + id);
        }

        String oldStatus = record.getStatus();
        if (ObjectUtil.equal(oldStatus, newStatus)) {
            return 1; // 状态相同，无需更新
        }

        // 更新状态
        record.setStatus(newStatus);
        record.setUpdateTime(DateUtils.getNowDate());
        int result = amBestDealRecordMapper.updateAmBestDealRecord(record);

        // 发布状态变更事件和记录日志
        if (result > 0) {
            PromotionEvent event = PromotionEvent.statusChangeEvent(
                    this, record, getCurrentUserId(), oldStatus, newStatus);
            eventPublisher.publishEvent(event);

            // 记录操作日志
            recordPromotionLog(id.intValue(), 1, "状态变更",
                    String.format("BD促销状态从 %s 变更为 %s", oldStatus, newStatus), 0, null);
        }

        return result;
    }

    /**
     * 按站点统计BD促销记录
     *
     * @return 统计结果
     */
    @Override
    public Map<String, Object> getStatsBySite() {
        try {
            List<Map<String, Object>> siteStats = amBestDealRecordMapper.getStatsBySite();
            Map<String, Object> result = new HashMap<>();
            result.put("siteStats", siteStats);

            // 计算总计
            int totalCount = siteStats.stream().mapToInt(stat -> (Integer) stat.get("count")).sum();
            result.put("totalCount", totalCount);

            // 计算各站点占比
            for (Map<String, Object> stat : siteStats) {
                int count = (Integer) stat.get("count");
                double percentage = totalCount > 0 ? (double) count / totalCount * 100 : 0;
                stat.put("percentage", Math.round(percentage * 100.0) / 100.0);
            }

            log.info("按站点统计BD促销记录完成，共{}个站点，{}条记录", siteStats.size(), totalCount);
            return result;

        } catch (Exception e) {
            log.error("按站点统计BD促销记录失败", e);
            return new HashMap<>();
        }
    }

    /**
     * 按状态统计BD促销记录
     *
     * @return 统计结果
     */
    @Override
    public Map<String, Object> getStatsByStatus() {
        try {
            List<Map<String, Object>> statusStats = amBestDealRecordMapper.getStatsByStatus();
            Map<String, Object> result = new HashMap<>();
            result.put("statusStats", statusStats);

            // 计算总计
            int totalCount = statusStats.stream().mapToInt(stat -> (Integer) stat.get("count")).sum();
            result.put("totalCount", totalCount);

            // 计算各状态占比
            for (Map<String, Object> stat : statusStats) {
                int count = (Integer) stat.get("count");
                double percentage = totalCount > 0 ? (double) count / totalCount * 100 : 0;
                stat.put("percentage", Math.round(percentage * 100.0) / 100.0);
            }

            log.info("按状态统计BD促销记录完成，共{}种状态，{}条记录", statusStats.size(), totalCount);
            return result;

        } catch (Exception e) {
            log.error("按状态统计BD促销记录失败", e);
            return new HashMap<>();
        }
    }

    /**
     * 获取BD促销记录总体统计
     *
     * @return 统计结果
     */
    @Override
    public Map<String, Object> getOverallStats() {
        try {
            Map<String, Object> result = new HashMap<>();

            // 总记录数
            int totalCount = amBestDealRecordMapper.getTotalCount();
            result.put("totalCount", totalCount);

            // 按状态统计
            Map<String, Object> statusStats = getStatsByStatus();
            result.put("statusStats", statusStats.get("statusStats"));

            // 按站点统计
            Map<String, Object> siteStats = getStatsBySite();
            result.put("siteStats", siteStats.get("siteStats"));

            // 按刊登类型统计
            List<Map<String, Object>> publishTypeStats = amBestDealRecordMapper.getStatsByPublishType();
            result.put("publishTypeStats", publishTypeStats);

            // 总ASIN数
            int totalAsins = amBestDealRecordMapper.getTotalAsinCount();
            result.put("totalAsins", totalAsins);

            // 最近7天创建的记录数
            int recentCreated = amBestDealRecordMapper.getRecentCreatedCount(7);
            result.put("recentCreated", recentCreated);

            // 就绪记录数（状态为APPROVED的记录）
            int readyCount = amBestDealRecordMapper.getCountByStatus("APPROVED");
            result.put("readyCount", readyCount);

            // 平均ASIN数
            double avgAsinCount = totalCount > 0 ? (double) totalAsins / totalCount : 0;
            result.put("avgAsinCount", Math.round(avgAsinCount * 100.0) / 100.0);

            // 创建趋势（最近30天）
            List<Map<String, Object>> createTrend = amBestDealRecordMapper.getCreateTrend(30);
            result.put("createTrend", createTrend);

            log.info("获取BD促销记录总体统计完成：总记录数={}, 总ASIN数={}, 最近7天创建={}",
                    totalCount, totalAsins, recentCreated);

            return result;

        } catch (Exception e) {
            log.error("获取BD促销记录总体统计失败", e);
            return new HashMap<>();
        }
    }

    /**
     * 处理ASIN变更（基于head_id进行精确比较）
     */
    private void processAsinChanges(Long refBestDealId, List<AmBestDealAsin> oldAsinList, List<AmBestDealAsin> newAsinList) {
        // 获取促销记录用于事件记录
        AmBestDealRecord record = selectAmBestDealRecordById(refBestDealId);

        // 使用基于head_id的变更比较和事件记录（传递完整record对象）
        if (record != null) {
            compareAndRecordAsinChangesByHeadId(record, oldAsinList, newAsinList);
        }

        // 先删除所有旧的ASIN记录
        amBestDealAsinMapper.deleteAmBestDealAsinByRefId(refBestDealId);

        // 插入新的ASIN记录
        if (CollUtil.isNotEmpty(newAsinList)) {
            for (AmBestDealAsin newAsin : newAsinList) {
                newAsin.setRefBestDealId(refBestDealId);
                newAsin.setCreateTime(DateUtils.getNowDate());
                amBestDealAsinMapper.insertAmBestDealAsin(newAsin);
            }
        }

        log.info("处理ASIN变更完成: refBestDealId={}, 旧ASIN数量={}, 新ASIN数量={}",
            refBestDealId,
            oldAsinList != null ? oldAsinList.size() : 0,
            newAsinList != null ? newAsinList.size() : 0);
    }

    /**
     * 发布UPDATE事件
     */
    private void publishUpdateEvent(AmBestDealRecord oldRecord, AmBestDealRecord newRecord, Map<String, Object> beforeData, Map<String, Object> afterData) {
        PromotionEvent event = PromotionEvent.updateEvent(
                this, oldRecord, newRecord, getCurrentUserId(), beforeData, afterData);
        eventPublisher.publishEvent(event);
    }

    /**
     * 将BD记录转换为Map
     */
    private Map<String, Object> convertRecordToMap(AmBestDealRecord record) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", record.getId());
        map.put("promotionId", record.getPromotionId());
        map.put("promotionName", record.getPromotionName());
        map.put("site", record.getSite());
        map.put("publishType", record.getPublishType());
        map.put("status", record.getStatus());
        map.put("dealType", record.getDealType());
        map.put("eventType", record.getEventType());
        map.put("startDateUtc", record.getStartDateUtc());
        map.put("endDateUtc", record.getEndDateUtc());
        map.put("createdDateTime", record.getCreatedDateTime());
        map.put("lastUpdateDateTime", record.getLastUpdateDateTime());
        return map;
    }

    /**
     * 记录促销操作日志
     */
    private void recordPromotionLog(Integer refId, Integer activityType, String title, String details, Integer status, String errorMsg) {
        try {
            promotionLogService.recordPromotionLog(refId, activityType, title, details, status, errorMsg);
        } catch (Exception e) {
            // 日志记录失败不应该影响主业务流程
            log.error("记录促销操作日志失败: refId={}, title={}, error={}", refId, title, e.getMessage());
        }
    }


    /**
     * 获取当前用户ID
     */
    private String getCurrentUserId() {
        try {
            return ShiroUtils.getSysUser().getUserId() +"";
        } catch (Exception e) {
            return "system";
        }
    }


    /**
     * 批量获取BD促销记录（按ID列表）
     *
     * @param ids ID列表
     * @return Map<ID, Record>
     */
    private Map<Long, AmBestDealRecord> batchGetRecordsByIds(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return new HashMap<>();
        }

        // 去重处理
        List<Long> uniqueIds = ids.stream()
            .distinct()
            .collect(Collectors.toList());

        Map<Long, AmBestDealRecord> result = new HashMap<>();

        // 分批查询，避免IN条件过长
        List<List<Long>> batches = CollUtil.split(uniqueIds, 100);

        for (List<Long> batch : batches) {
            List<AmBestDealRecord> records = amBestDealRecordMapper.selectAmBestDealRecordByIds(batch);

            for (AmBestDealRecord record : records) {
                // 加载关联的ASIN列表
                List<AmBestDealAsin> asinList = amBestDealAsinMapper.selectAmBestDealAsinByRefId(record.getId());
                record.setAsinList(asinList);

                result.put(record.getId(), record);
            }
        }

        return result;
    }

    /**
     * 批量删除关联的ASIN记录
     *
     * @param recordMap 记录Map
     */
    private void batchDeleteRelatedAsins(Map<Long, AmBestDealRecord> recordMap) {
        for (Map.Entry<Long, AmBestDealRecord> entry : recordMap.entrySet()) {
            Long recordId = entry.getKey();
            AmBestDealRecord record = entry.getValue();

            if (record == null) {
                continue;
            }

            List<AmBestDealAsin> asinList = record.getAsinList();
            if (CollUtil.isNotEmpty(asinList)) {
                try {
                    // 发布ASIN删除事件
                    for (AmBestDealAsin asin : asinList) {
                        PromotionAsinEvent asinDeleteEvent = PromotionAsinEvent.asinRemoveEvent(
                            this, record, asin, getCurrentUserId());
                        eventPublisher.publishEvent(asinDeleteEvent);
                    }

                    // 删除ASIN关联数据
                    amBestDealAsinMapper.deleteAmBestDealAsinByRefId(recordId);

                    log.info("删除BD记录关联ASIN成功: recordId={}, asinCount={}",
                        recordId, asinList.size());

                } catch (Exception e) {
                    log.error("删除BD记录关联ASIN失败: recordId={}, error={}", recordId, e.getMessage(), e);
                    throw new RuntimeException("删除关联ASIN失败: " + e.getMessage());
                }
            }
        }
    }

    /**
     * 批量发布删除事件
     *
     * @param recordMap 记录Map
     */
    private void batchPublishDeleteEvents(Map<Long, AmBestDealRecord> recordMap) {
        for (Map.Entry<Long, AmBestDealRecord> entry : recordMap.entrySet()) {
            Long id = entry.getKey();
            AmBestDealRecord record = entry.getValue();

            if (record == null) {
                continue;
            }

            try {
                PromotionEvent event = PromotionEvent.deleteEvent(this, record, getCurrentUserId());
                eventPublisher.publishEvent(event);

                // 记录操作日志
                int asinCount = record.getAsinList() != null ? record.getAsinList().size() : 0;
                recordPromotionLog(id.intValue(), 1, "批量删除BD促销",
                    String.format("批量删除BD促销记录：%s，包含%d个ASIN",
                        record.getPromotionName(), asinCount), 0, null);

            } catch (Exception e) {
                log.error("发布删除事件失败: id={}, error={}", id, e.getMessage(), e);
            }
        }
    }


    // =====================================================
    // 基于head_id的ASIN变更比较方法
    // =====================================================

    /**
     * 比较ASIN列表变更并记录事件（基于head_id，使用完整record对象）
     *
     * @param record BD记录对象
     * @param oldAsinList 原ASIN列表
     * @param newAsinList 新ASIN列表
     */
    private void compareAndRecordAsinChangesByHeadId(AmBestDealRecord record,
                                                    List<AmBestDealAsin> oldAsinList,
                                                    List<AmBestDealAsin> newAsinList) {
        if (oldAsinList == null) oldAsinList = new ArrayList<>();
        if (newAsinList == null) newAsinList = new ArrayList<>();

        // 转换为head_id集合进行比较
        Set<Long> oldHeadIds = oldAsinList.stream()
            .map(AmBestDealAsin::getHeadId)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());

        Set<Long> newHeadIds = newAsinList.stream()
            .map(AmBestDealAsin::getHeadId)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());

        // 找出新增的head_id
        Set<Long> addedHeadIds = new HashSet<>(newHeadIds);
        addedHeadIds.removeAll(oldHeadIds);

        // 找出移除的head_id
        Set<Long> removedHeadIds = new HashSet<>(oldHeadIds);
        removedHeadIds.removeAll(newHeadIds);

        // 找出保持的head_id（用于检查其他字段变更）
        Set<Long> retainedHeadIds = new HashSet<>(oldHeadIds);
        retainedHeadIds.retainAll(newHeadIds);

        // 记录新增事件（使用完整record对象）
        recordAsinAddEventsByHeadId(record, newAsinList, addedHeadIds);

        // 记录移除事件（使用完整record对象）
        recordAsinRemoveEventsByHeadId(record, oldAsinList, removedHeadIds);

        // 记录更新事件（同一head_id但其他字段有变更，使用完整record对象）
        recordAsinUpdateEventsByHeadId(record, oldAsinList, newAsinList, retainedHeadIds);

        log.info("ASIN变更记录完成: recordId={}, promotionId={}, 新增={}, 移除={}, 更新检查={}",
                record.getId(), record.getPromotionId(), addedHeadIds.size(), removedHeadIds.size(), retainedHeadIds.size());
    }

    /**
     * 记录ASIN新增事件（基于head_id，使用完整record对象）
     */
    private void recordAsinAddEventsByHeadId(AmBestDealRecord record,
                                           List<AmBestDealAsin> newAsinList, Set<Long> addedHeadIds) {
        for (AmBestDealAsin asin : newAsinList) {
            if (asin.getHeadId() != null && addedHeadIds.contains(asin.getHeadId())) {
                try {
                    PromotionAsinEvent event = PromotionAsinEvent.asinAddEvent(
                        this, record, asin, getCurrentUserId());
                    eventPublisher.publishEvent(event);

                    log.debug("发布ASIN新增事件: headId={}, platformGoodsId={}",
                        asin.getHeadId(), asin.getPlatformGoodsId());

                } catch (Exception e) {
                    log.error("发布ASIN新增事件失败: headId={}, error={}", asin.getHeadId(), e.getMessage(), e);
                }
            }
        }
    }

    /**
     * 记录ASIN移除事件（基于head_id，使用完整record对象）
     */
    private void recordAsinRemoveEventsByHeadId(AmBestDealRecord record,
                                              List<AmBestDealAsin> oldAsinList, Set<Long> removedHeadIds) {
        for (AmBestDealAsin asin : oldAsinList) {
            if (asin.getHeadId() != null && removedHeadIds.contains(asin.getHeadId())) {
                try {
                    PromotionAsinEvent event = PromotionAsinEvent.asinRemoveEvent(
                        this, record, asin, getCurrentUserId());
                    eventPublisher.publishEvent(event);

                    log.debug("发布ASIN移除事件: headId={}, platformGoodsId={}",
                        asin.getHeadId(), asin.getPlatformGoodsId());

                } catch (Exception e) {
                    log.error("发布ASIN移除事件失败: headId={}, error={}", asin.getHeadId(), e.getMessage(), e);
                }
            }
        }
    }

    /**
     * 记录ASIN更新事件（基于head_id，检查同一head_id的其他字段变更，使用完整record对象）
     */
    private void recordAsinUpdateEventsByHeadId(AmBestDealRecord record,
                                              List<AmBestDealAsin> oldAsinList,
                                              List<AmBestDealAsin> newAsinList,
                                              Set<Long> retainedHeadIds) {
        // 构建head_id到ASIN的映射
        Map<Long, AmBestDealAsin> oldAsinMap = oldAsinList.stream()
            .filter(asin -> asin.getHeadId() != null)
            .collect(Collectors.toMap(AmBestDealAsin::getHeadId, asin -> asin));

        Map<Long, AmBestDealAsin> newAsinMap = newAsinList.stream()
            .filter(asin -> asin.getHeadId() != null)
            .collect(Collectors.toMap(AmBestDealAsin::getHeadId, asin -> asin));

        for (Long headId : retainedHeadIds) {
            AmBestDealAsin oldAsin = oldAsinMap.get(headId);
            AmBestDealAsin newAsin = newAsinMap.get(headId);

            if (oldAsin != null && newAsin != null) {
                // 检查是否有字段变更（排除ID和时间戳字段）
                if (hasAsinFieldChanges(oldAsin, newAsin)) {
                    try {
                        Map<String, Object> beforeData = convertAsinToMap(oldAsin);
                        Map<String, Object> afterData = convertAsinToMap(newAsin);

                        PromotionAsinEvent event = PromotionAsinEvent.asinUpdateEvent(
                            this, record, oldAsin, newAsin, getCurrentUserId(), beforeData, afterData);
                        eventPublisher.publishEvent(event);

                        log.debug("发布ASIN更新事件: headId={}, platformGoodsId={}",
                            headId, newAsin.getPlatformGoodsId());

                    } catch (Exception e) {
                        log.error("发布ASIN更新事件失败: headId={}, error={}", headId, e.getMessage(), e);
                    }
                }
            }
        }
    }

    /**
     * 检查ASIN字段是否有变更
     */
    private boolean hasAsinFieldChanges(AmBestDealAsin oldAsin, AmBestDealAsin newAsin) {
        return !Objects.equals(oldAsin.getStandardPrice(), newAsin.getStandardPrice()) ||
               !Objects.equals(oldAsin.getReferencePrice(), newAsin.getReferencePrice()) ||
               !Objects.equals(oldAsin.getExpectedDemand(), newAsin.getExpectedDemand()) ||
               !Objects.equals(oldAsin.getCommittedUnits(), newAsin.getCommittedUnits()) ||
               !Objects.equals(oldAsin.getLowestDiscount(), newAsin.getLowestDiscount()) ||
               !Objects.equals(oldAsin.getActualDiscount(), newAsin.getActualDiscount()) ||
               !Objects.equals(oldAsin.getDealPrice(), newAsin.getDealPrice()) ||
               !Objects.equals(oldAsin.getPerUnitFunding(), newAsin.getPerUnitFunding());
    }

    /**
     * 将ASIN对象转换为Map
     */
    private Map<String, Object> convertAsinToMap(AmBestDealAsin asin) {
        Map<String, Object> map = new HashMap<>();
        map.put("headId", asin.getHeadId());
        map.put("platformGoodsId", asin.getPlatformGoodsId());
        map.put("platformGoodsCode", asin.getPlatformGoodsCode());
        map.put("pdmGoodsCode", asin.getPdmGoodsCode());
        map.put("standardPrice", asin.getStandardPrice());
        map.put("referencePrice", asin.getReferencePrice());
        map.put("expectedDemand", asin.getExpectedDemand());
        map.put("committedUnits", asin.getCommittedUnits());
        map.put("lowestDiscount", asin.getLowestDiscount());
        map.put("actualDiscount", asin.getActualDiscount());
        map.put("dealPrice", asin.getDealPrice());
        map.put("perUnitFunding", asin.getPerUnitFunding());
        return map;
    }

    /**
     * 获取所有不重复的状态值
     *
     * @return 状态列表
     */
    @Override
    public List<String> getDistinctStatusList() {
        return amBestDealRecordMapper.selectDistinctStatusList();
    }

    /**
     * 根据促销ID更新状态
     *
     * @param promotionId 促销ID
     * @param status      新状态
     * @param updateBy    更新人
     * @return 结果
     */
    @Override
    public int updateStatusByPromotionId(String promotionId, String status, String updateBy) {
        return amBestDealRecordMapper.updateStatusByPromotionId(promotionId, status, updateBy);
    }

    /**
     * 批量确认BD促销记录
     * 委托给AmazonVcPromotionBiz处理
     *
     * @param ids 记录ID数组
     * @param operatorId 操作人ID
     * @return 成功确认的记录数量
     */
    @Override
    public int batchConfirmBdPromotions(String[] ids, String operatorId) {
        // 这个方法将在控制器中直接调用AmazonVcPromotionBiz
        throw new UnsupportedOperationException("请直接调用AmazonVcPromotionBiz.batchConfirmBdPromotions方法");
    }

    /**
     * 批量取消BD促销记录
     * 委托给AmazonVcPromotionBiz处理
     *
     * @param ids 记录ID数组
     * @param operatorId 操作人ID
     * @return 成功取消的记录数量
     */
    @Override
    public int batchCancelBdPromotions(String[] ids, String operatorId) {
        // 这个方法将在控制器中直接调用AmazonVcPromotionBiz
        throw new UnsupportedOperationException("请直接调用AmazonVcPromotionBiz.batchCancelBdPromotions方法");
    }
}
