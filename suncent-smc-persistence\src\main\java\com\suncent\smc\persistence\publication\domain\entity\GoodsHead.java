package com.suncent.smc.persistence.publication.domain.entity;

import cn.hutool.core.util.ObjUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.suncent.smc.common.annotation.Excel;
import com.suncent.smc.common.core.domain.BaseEntity;
import com.suncent.smc.persistence.amazon.domain.VcListingInventory;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 商品刊登头部数据实体类 sc_smc_listing_goods_head
 *
 * <AUTHOR>
 * @since 2023-01-11 19:54:00
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Slf4j
public class GoodsHead extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Excel(name = "主键ID（不允许修改此值！）")
    private Integer id;

    /**
     * 商品编码-同步自PDM系统
     */
    @Excel(name = "PDM商品编码（AM不允许修改此值！）")
    private String pdmGoodsCode;

    /**
     * 平台商品编码
     */
    @Excel(name = "平台商品编码（AM不允许修改此值！）")
    private String platformGoodsCode;

    /**
     * 商品平台ID-刊登成功后平台返回的ID
     */
    @Excel(name = "商品平台ID（不允许修改此值！）")
    private String platformGoodsId;

    /**
     * 平台
     */
    private String platform;

    /**
     * 店铺
     */
    private String shopCode;


    private List<String> shopCodes;

    /**
     * 刊登类型 0FBM，1FBA，２多变，３固定，４拍卖，5VCDF,6VCPO
     */
    private Integer publishType;

    private List<Integer> publishTypeList;

    /**
     * 商品状况
     */
    private String condition;

    /**
     * 标题信息
     */
    @Excel(name = "标题信息")
    private String title;

    /**
     * 副标题信息
     */
    private String subtitle;

    /**
     * 刊登状态，0未刊登，草稿状态 1刊登中 2在售状态 3更新中 4更新失败 5下架中 6非在售 7下架失败 8刊登失败
     */
    private Integer publishStatus;


    /**
     * 数据适配状态，待适配，适配成功，适配失败,适配异常
     */
    @Excel(name = "适配状态")
    private String adaptationStatus;

    private List<String> adaptationStatusList;

    /**
     * 商品主图
     */
    private String mainImageUrl;

    /**
     * 站点
     */
    private String siteCode;

    /**
     * 品牌编码
     */
    @Excel(name = "品牌编码")
    private String brandCode;

    private List<String> brandCodeList;
    /**
     * 当前售价
     */
    @Excel(name = "当前售价")
    private String standardPrice;
    /**
     * 刊登原价
     */
    @Excel(name = "刊登原价")
    private String originalPrice;

    @Excel(name = "结算价")
    private BigDecimal settlementPrice;

    @Excel(name = "Cost Price")
    private String vcCostPrice;

    @Excel(name = "红线价")
    private BigDecimal redLinePrice;
    /**
     * 库存
     */
    @Excel(name = "库存")
    private BigDecimal stockOnSalesQty;

    /**1
     * 实时库存
     */
    @Excel(name = "实际库存")
    private String realStockOnSalesQty;

    /**
     * 类目id
     */
    private Integer categoryId;
    private String categoryName;

    private String categoryIds;

    /**
     * 删除标志（0代表存在 2代表删除 3代表自动生成）
     */
    private Integer delFlag;
    /**
     * PDM映射状态
     */
    private Integer pdmStatus;

    /**
     * 刊登中状态,（处理中、已处理）
     * ebay图片上传中状态,（处理中、已处理）
     *
     * 刊登中状态是处理中、并且刊登状态是刊登中 时，不能进行刊登操作
     */
    private String publishingHandler;
    /**
     * 刊登状态集合
     */
    private List<Integer> publishStatusList;

    /**
     * itemid 集合
     */
    private List<String>   platformGoodsIdList;
    /**
     * 平台商品编码集合
     */
    private List<String> platformGoodsCodeList;

    /**
     * 上架时间
     */
    @Excel(name = "上架时间",  dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date onlineTime;

    /**
     * 下架时间
     */
    @Excel(name = "下架时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date offTime;

    /**
     * 自动生成任务名称
     */
    private String taskName;


    private String ids;
    @Excel(name = "FnSku")
    private String fnSku;

    /**
     * 0,smc手动刊登 1,同步回来的listing 2，自动刊登
     */
    private Integer smcFlag;
    private List<Integer> smcFlagList;

    private List<ListingAmazonAttributeLineV2> attributeLines;

    private List<VcListingInventory> vcListingInventoryList;
    /**
     * remark 字段获取json
     * @return
     */
    public Map<String,Object> getRemarkJSON(){
        try {
            if (ObjUtil.isEmpty(super.getRemark())) {
                return new HashMap();
            }
            return JSONObject.parseObject(super.getRemark()).getInnerMap();
        }catch (Exception e){
            log.error(e.getMessage(),e);
        }
        return new HashMap();
    }

    /**
     * remark setjson
     * @return
     */
    public void setRemarkJSON(String key,String value){
        Map<String, Object> remarkJSON = getRemarkJSON();
        remarkJSON.put(key,value);
        super.setRemark(JSON.toJSONString(remarkJSON));

        remarkJSON=null;
    }

    private  List<String> skuList;

    private List<String> idList;

    /**销售编码为空标记 1则为空*/
    private Integer asinNullFlag;

    /**送检结果 0送检失败1送检成功 2未送检*/
    private String censorship;


    /**校验类型*/
    private String parityBit;

    private String standardPriceStart;
    private String standardPriceEnd;
    private BigDecimal stockOnSalesQtyStart;
    private BigDecimal stockOnSalesQtyEnd;
    private Integer descriptionId;
    private String videoId;
//    @Excel(name = "listing退货率标签")
    private String listingRateLabel;
//    @Excel(name = "sku退货率标签")
    private String skuRateLabel;
    // 不在主sheet显示，仅用于内部处理和详情sheet
    private String listingPerformance;

    private String pnCode;

    private String descTemplateName;

    private String aplusTemplateName;

    private String saleValueStart;
    private String saleValueEnd;
    private String orderValueStart;
    private String orderValueEnd;
    private Integer saleType;
    private Integer orderType;
    /** 红线价*/
    private String redLinePriceFlag;

    private String now;

    private String platCategoryId;

    private Integer isMain;

    private Integer cartLabel;
    /**
     * 独立站专用
     */
    private String independentSiteOnly;

    private String queryTime;

    /**
     * 不存在图片资源
     */
    private Boolean nonResource;

    @Excel(name = "系列")
    private String productSeries;
    /**
     * 库存黑名单
     */
    private String inventoryExclude;

    @Excel(name = "型号")
    private String productModel;
    /**
     * 是否库存黑名单 1是，0非
     */
    private Integer isInventoryBlack;

    private Integer isRedLineBlack;

    private Integer isCouponBlack;

    private String redLineBlackFlag;

    private String blackSku;
    private String blackGoodsId;

    private Integer currentAvailability;
    private Boolean hasIssues;

    private Date pullDate;

    /**
     * 业务分类列表（查询条件）
     */
    private List<String> operationClassificationList;

    private BigDecimal vcListPrice;
}