package com.suncent.smc.persistence.promotion.service;

import com.suncent.smc.persistence.promotion.domain.entity.AmBestDealRecord;
import com.suncent.smc.persistence.publication.domain.entity.GoodsHead;

import java.util.List;
import java.util.Map;

/**
 * BD促销记录Service接口
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface IAmBestDealRecordService {
    /**
     * 查询BD促销记录
     * 
     * @param id BD促销记录主键
     * @return BD促销记录
     */
    public AmBestDealRecord selectAmBestDealRecordById(Long id);

    /**
     * 查询BD促销记录列表
     *
     * @param amBestDealRecord BD促销记录
     * @return BD促销记录集合
     */
    public List<AmBestDealRecord> selectAmBestDealRecordList(AmBestDealRecord amBestDealRecord);

    /**
     * 查询BD促销记录列表（包含ASIN数量和ASIN列表数据）
     *
     * @param amBestDealRecord BD促销记录
     * @return BD促销记录集合
     */
    public List<AmBestDealRecord> selectAmBestDealRecordListWithAsinData(AmBestDealRecord amBestDealRecord);

    /**
     * 新增BD促销记录
     * 
     * @param amBestDealRecord BD促销记录
     * @return 结果
     */
    public int insertAmBestDealRecord(AmBestDealRecord amBestDealRecord);


    public int saveAmBestDealRecord(AmBestDealRecord amBestDealRecord, Map<Integer, GoodsHead> goodsHeadMap);

    /**
     * 修改BD促销记录
     * 
     * @param amBestDealRecord BD促销记录
     * @return 结果
     */
    public int updateAmBestDealRecord(AmBestDealRecord amBestDealRecord);

    /**
     * 批量删除BD促销记录
     * 
     * @param ids 需要删除的BD促销记录主键集合
     * @return 结果
     */
    public int deleteAmBestDealRecordByIds(String ids);

    /**
     * 删除BD促销记录信息
     * 
     * @param id BD促销记录主键
     * @return 结果
     */
    public int deleteAmBestDealRecordById(Long id);

    /**
     * 批量新增BD促销记录
     *
     * @param records BD促销记录列表
     * @return 结果
     */
    public int batchInsertAmBestDealRecord(List<AmBestDealRecord> records);

    /**
     * 根据促销ID查询BD记录
     *
     * @param promotionId 促销ID
     * @return BD促销记录
     */
    public AmBestDealRecord selectAmBestDealRecordByPromotionId(String promotionId);

    /**
     * 根据促销ID列表批量查询BD记录
     *
     * @param promotionIds 促销ID列表
     * @return BD促销记录集合
     */
    public List<AmBestDealRecord> selectAmBestDealRecordByPromotionIds(List<String> promotionIds);

    /**
     * 根据站点和状态查询BD记录列表
     *
     * @param site 站点
     * @param status 状态
     * @return BD促销记录集合
     */
    public List<AmBestDealRecord> selectAmBestDealRecordBySiteAndStatus(String site, String status);

    /**
     * 更新BD促销记录状态
     *
     * @param id BD记录ID
     * @param newStatus 新状态
     * @return 结果
     */
    public int updateAmBestDealRecordStatus(Long id, String newStatus);

    /**
     * 按站点统计BD促销记录
     *
     * @return 统计结果
     */
    public Map<String, Object> getStatsBySite();

    /**
     * 按状态统计BD促销记录
     *
     * @return 统计结果
     */
    public Map<String, Object> getStatsByStatus();

    /**
     * 获取BD促销记录总体统计
     *
     * @return 统计结果
     */
    public Map<String, Object> getOverallStats();

    /**
     * 获取所有不重复的状态值
     *
     * @return 状态列表
     */
    public List<String> getDistinctStatusList();

    /**
     * 根据促销ID更新状态
     *
     * @param promotionId 促销ID
     * @param status      新状态
     * @param updateBy    更新人
     * @return 结果
     */
    public int updateStatusByPromotionId(String promotionId, String status, String updateBy);

    /**
     * 批量确认BD促销记录
     * 将草稿状态的记录更新为UPDATING状态，并创建RPA任务
     *
     * @param ids 记录ID数组
     * @param operatorId 操作人ID
     * @return 成功确认的记录数量
     */
    public int batchConfirmBdPromotions(String[] ids, String operatorId);

    /**
     * 批量取消BD促销记录
     * 将更新中状态的记录更新为CANCELED状态，并创建取消RPA任务
     *
     * @param ids 记录ID数组
     * @param operatorId 操作人ID
     * @return 成功取消的记录数量
     */
    public int batchCancelBdPromotions(String[] ids, String operatorId);
}
